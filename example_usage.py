#!/usr/bin/env python3
"""
多阶段模拟系统使用示例
"""

import sys
import os
sys.path.append('src')

from multi_stage_simulation import MultiStageSimulation

def example_single_vote_simulation():
    """示例：运行单个投票的完整模拟"""
    print("=== 单个投票模拟示例 ===")
    
    # 使用测试投票ID
    vote_id = 167680
    vote_title = "Urban wastewater treatment"
    
    print(f"模拟投票: {vote_id} - {vote_title}")
    
    # 创建模拟实例
    simulation = MultiStageSimulation(
        vote_id=vote_id,
        vote_title=vote_title,
        output_dir="./example_output"
    )
    
    print(f"初始化完成，共有 {len(simulation.party_groups)} 个党团")
    
    # 显示党团信息
    for group_code, group_df in simulation.party_groups.items():
        print(f"  - {group_code}: {len(group_df)} 名议员")
    
    print("\n注意：实际运行需要LLM API，这里只演示初始化")
    print("要运行完整模拟，请使用:")
    print(f"python src/main_multi_stage.py --single_vote {vote_id}")
    
    return simulation

def example_analyze_json_structure():
    """示例：分析生成的JSON文件结构"""
    print("\n=== JSON文件结构分析示例 ===")
    
    import json
    
    # 检查是否有测试输出
    test_file = "test_output/proposal_167680/EPP.json"
    if os.path.exists(test_file):
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"党团信息:")
        print(f"  - 代码: {data['group_info']['group_code']}")
        print(f"  - 名称: {data['group_info']['group_name']}")
        print(f"  - 成员数: {data['group_info']['total_members']}")
        
        print(f"\n投票统计:")
        for stage, stats in data['voting_statistics'].items():
            print(f"  - {stage}: {stats}")
        
        print(f"\n议员数据结构:")
        if data['agents']:
            agent = data['agents'][0]
            print(f"  - 议员ID: {agent['agent_id']}")
            print(f"  - 姓名: {agent['static_profile']['basic_info']['fullname']}")
            print(f"  - 党团: {agent['static_profile']['political_identity']['group_code']}")
            print(f"  - 国家: {agent['static_profile']['basic_info']['country']}")
            
            # 显示JSON结构的主要部分
            print(f"\n  主要数据结构:")
            for key in agent.keys():
                if key not in ['agent_id', 'vote_id']:
                    print(f"    - {key}")
    else:
        print("未找到测试输出文件，请先运行 python test_multi_stage.py")

def example_custom_sampling():
    """示例：自定义采样比例"""
    print("\n=== 自定义采样示例 ===")
    
    # 修改全局采样比例
    import multi_stage_simulation
    original_ratio = multi_stage_simulation.SAMPLING_RATIO
    
    print(f"默认采样比例: {original_ratio * 100}%")
    
    # 设置为10%
    multi_stage_simulation.SAMPLING_RATIO = 0.1
    print(f"修改后采样比例: {multi_stage_simulation.SAMPLING_RATIO * 100}%")
    
    # 恢复原始值
    multi_stage_simulation.SAMPLING_RATIO = original_ratio
    print(f"恢复采样比例: {multi_stage_simulation.SAMPLING_RATIO * 100}%")

def example_batch_processing():
    """示例：批量处理多个投票"""
    print("\n=== 批量处理示例 ===")
    
    # 这里只是演示逻辑，实际运行需要LLM API
    vote_ids = [167680, 164499]  # 示例投票ID列表
    
    print("批量处理投票列表:")
    for vote_id in vote_ids:
        print(f"  - 投票 {vote_id}: 准备处理")
    
    print("\n实际批量处理命令:")
    print(f"python src/main_multi_stage.py --vote_list {' '.join(map(str, vote_ids))}")

def main():
    """主函数"""
    print("🚀 多阶段模拟系统使用示例")
    print("=" * 50)
    
    # 示例1：单个投票模拟
    simulation = example_single_vote_simulation()
    
    # 示例2：分析JSON结构
    example_analyze_json_structure()
    
    # 示例3：自定义采样
    example_custom_sampling()
    
    # 示例4：批量处理
    example_batch_processing()
    
    print("\n" + "=" * 50)
    print("📚 更多使用方法请参考 MULTI_STAGE_README.md")
    print("🧪 运行测试: python test_multi_stage.py")
    print("🚀 开始模拟: python src/main_multi_stage.py --single_vote 167680")

if __name__ == "__main__":
    main()
