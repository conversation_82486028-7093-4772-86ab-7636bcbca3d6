# 结果分析功能说明

## 概述

我们已经成功创建了一个完整的结果分析系统，用于计算多阶段模拟的评估指标。该系统实现了以下四个核心功能：

1. **准确率**：整体预测准确性的基础指标
2. **F1分数**：与原文保持一致的整体预测准确性评估
3. **弃权票F1分数**：专门评估对"弃权"这一难点的预测能力
4. **党团凝聚力得分**：计算预测结果中各党团内部投票的一致性，并与真实投票的凝聚力进行比较

## 文件结构

### 新增文件

1. **`src/result_analysis.py`** - 主要的结果分析脚本
2. **`requirements.txt`** - 更新了依赖，添加了 `scikit-learn` 和 `numpy`
3. **`RESULT_ANALYSIS_README.md`** - 本说明文档

### 修改文件

1. **`src/main_multi_stage.py`** - 添加了自动调用结果分析的功能

## 功能详细说明

### 1. 准确率计算

- **整体准确率**：正确预测的样本数量占总样本数量的比例
- 提供最直观的预测性能评估

### 2. F1分数计算

- **宏平均F1分数**：所有类别（FOR, AGAINST, ABSTENTION）的F1分数平均值
- **微平均F1分数**：基于总体预测准确性的F1分数
- **加权F1分数**：根据各类别样本数量加权的F1分数
- **各类别F1分数**：FOR、AGAINST、ABSTENTION 各自的F1分数

### 3. 弃权票F1分数

专门针对弃权票的二分类评估：
- 将问题转换为：弃权 vs 非弃权
- 计算精确率、召回率、F1分数
- 提供详细的混淆矩阵信息
- 统计实际和预测的弃权票数量

### 4. 党团凝聚力得分

使用基尼不纯度来衡量党团内部投票的一致性：
- **凝聚力分数** = 1 - 基尼不纯度
- 计算各党团的实际凝聚力和预测凝聚力
- 比较实际与预测凝聚力的相似性
- 提供党团级别和整体的凝聚力统计

## 使用方法

### 自动分析（推荐）

运行多阶段模拟后，系统会自动调用结果分析：

```bash
# 单个投票模拟（会自动分析）
python src/main_multi_stage.py --single_vote 162202

# 批量投票模拟（会自动分析所有结果）
python src/main_multi_stage.py --vote_list 162202 167680
```

### 手动分析

也可以手动对现有结果进行分析：

```bash
# 分析单个结果文件
python src/result_analysis.py simulation_results/proposal_162202/final_results_162202.csv

# 指定输出目录
python src/result_analysis.py simulation_results/proposal_162202/final_results_162202.csv ./custom_output/
```

## 输出文件

分析完成后会在相应目录生成两个文件：

### 1. `analysis_report.json`
详细的JSON格式报告，包含：
- 元数据信息（总议员数、党团数等）
- 三个阶段的完整F1分数分析
- 弃权票的详细预测分析
- 党团凝聚力的完整分析
- 分类报告

### 2. `analysis_summary.csv`
简化的CSV格式摘要，包含：
- 各阶段的关键指标
- 便于进一步分析和可视化

## 示例结果

基于现有的 proposal_162202 数据，分析结果显示：

### Stage 3 (最终阶段) 关键指标：
- **准确率**: 0.904
- **F1分数 (宏平均)**: 0.649
- **F1分数 (加权)**: 0.887
- **弃权票F1分数**: 0.133
- **党团凝聚力相似性**: 0.881

### 弃权票预测分析：
- 实际弃权票数：26
- 预测弃权票数：4
- 精确率：1.000
- 召回率：0.154

### 党团凝聚力分析：
各党团的实际凝聚力与预测凝聚力高度相似，整体相似性达到 0.881，表明模型能够较好地模拟党团内部的聚合效应。

## 技术实现

### 核心算法

1. **F1分数计算**：使用 sklearn.metrics.f1_score
2. **凝聚力计算**：基于基尼不纯度的自定义算法
3. **相似性评估**：基于绝对差异的标准化相似性度量

### 数据处理

- 自动解析字符串格式的投票列表
- 处理缺失值和异常数据
- 支持三阶段投票结果的分别分析

### 错误处理

- 完善的异常处理机制
- 自动回退和错误报告
- 不影响主要模拟流程的运行

## 扩展性

该分析系统设计为模块化，可以轻松扩展：

1. **新增评估指标**：在 `ResultAnalyzer` 类中添加新方法
2. **自定义输出格式**：修改 `save_results` 方法
3. **批量分析功能**：已支持多文件批量处理
4. **可视化支持**：JSON和CSV输出便于后续可视化

## 依赖要求

确保安装了以下依赖：

```bash
pip install scikit-learn>=1.3.0 numpy>=1.24.0 pandas>=2.3.0
```

或者直接安装更新后的requirements.txt：

```bash
pip install -r requirements.txt
```

## 注意事项

1. 分析脚本会自动处理编码问题，支持中文输出
2. 如果某个阶段的数据不完整，会使用默认值填充
3. 党团凝聚力分析要求至少有2个成员的党团才有意义
4. 弃权票F1分数可能较低，这是正常现象，因为弃权票通常是少数类别

## 故障排除

如果遇到问题：

1. 检查CSV文件格式是否正确
2. 确保所有依赖都已安装
3. 检查文件路径是否正确
4. 查看错误日志获取详细信息

---

该结果分析系统为欧洲议会投票模拟提供了全面的评估工具，帮助研究人员更好地理解和评估模型的性能。
