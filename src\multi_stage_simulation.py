import pandas as pd
import json
import os
import random
import math
from datetime import datetime
from typing import Dict, List, Tuple, Any
from collections import defaultdict
from tqdm import tqdm

from utilities.apiLLM import batch_generation
from utilities.utils import parse_json, calculate_age
from utilities.promptCreation import PromptCreation, PersonaCall

# 统一数据根路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

VOTE_OPTIONS = ["FOR", "AGAINST", "ABSTENTION"]

class MultiStageSimulation:
    """多阶段欧洲议会投票模拟系统"""
    
    def __init__(self, vote_id: int, vote_title: str, output_dir: str = "./simulation_results",
                 party_sampling_ratio: float = 0.05, public_sampling_ratio: float = 0.05):
        self.vote_id = vote_id
        self.vote_title = vote_title
        self.output_dir = output_dir
        self.proposal_dir = os.path.join(output_dir, f"proposal_{vote_id}")

        # 采样比例设置
        self.party_sampling_ratio = party_sampling_ratio    # 党团会议采样比例
        self.public_sampling_ratio = public_sampling_ratio  # 公开辩论采样比例
        
        # 创建输出目录
        os.makedirs(self.proposal_dir, exist_ok=True)
        
        # 加载数据
        self.members_df = self._load_members_data()
        self.speeches = self._load_speeches()
        self.actual_votes = self._load_actual_votes()
        
        # 按党团组织数据
        self.party_groups = self._organize_by_party_groups()
        
        # 初始化党团JSON文件
        self._initialize_party_group_files()
    
    def _load_members_data(self) -> pd.DataFrame:
        """加载议员数据"""
        members_path = os.path.join(BASE_DIR, "data", "personas", "members_voting_time.csv")
        member_votes_path = os.path.join(BASE_DIR, "data", "howTheyVoteDataSet", "member_votes.csv")
        votes_path = os.path.join(BASE_DIR, "data", "howTheyVoteDataSet", "votes.csv")
        
        df_members = pd.read_csv(members_path)
        df_member_votes = pd.read_csv(member_votes_path)
        df_votes = pd.read_csv(votes_path)
        
        # 获取投票时间
        row_votes = df_votes[df_votes["id"] == self.vote_id].iloc[0]
        timestamp = pd.to_datetime(row_votes["timestamp"]).date()
        
        # 筛选在任议员
        df_members["start_date"] = pd.to_datetime(df_members["start_date"]).dt.date
        df_members["end_date"] = pd.to_datetime(df_members["end_date"]).dt.date
        df_members = df_members[(df_members["start_date"] <= timestamp) & 
                               (df_members["end_date"] >= timestamp)]
        
        # 合并投票数据
        df_specific_votes = df_member_votes[df_member_votes["vote_id"] == self.vote_id]
        df_result = df_specific_votes.merge(df_members, left_on="member_id", right_on="member_id", how="inner")
        df_result = df_result[df_result["position"] != "DID_NOT_VOTE"]

        # 处理合并后的列名冲突
        if 'group_code_y' in df_result.columns:
            df_result['group_code'] = df_result['group_code_y']
            df_result = df_result.drop(['group_code_x', 'group_code_y'], axis=1)
        elif 'group_code_x' in df_result.columns:
            df_result['group_code'] = df_result['group_code_x']
            df_result = df_result.drop(['group_code_x'], axis=1)

        return df_result
    
    def _load_speeches(self) -> List[str]:
        """加载辩论发言"""
        speeches_path = os.path.join(BASE_DIR, "data", "debates", "speeches.csv")
        df_speeches = pd.read_csv(speeches_path)
        df_speeches = df_speeches[df_speeches['id'] == self.vote_id]
        
        speeches = df_speeches["modified_speech"].to_list()
        if len(speeches) == 0:
            raise RuntimeError("No speeches found")
        
        random.shuffle(speeches)
        return speeches
    
    def _load_actual_votes(self) -> Dict[int, str]:
        """加载实际投票结果"""
        actual_votes = {}
        for _, row in self.members_df.iterrows():
            actual_votes[row["member_id"]] = row["position"]
        return actual_votes
    
    def _organize_by_party_groups(self) -> Dict[str, pd.DataFrame]:
        """按党团组织议员数据"""
        party_groups = {}
        for group_code in self.members_df["group_code"].unique():
            party_groups[group_code] = self.members_df[self.members_df["group_code"] == group_code]
        return party_groups
    
    def _create_agent_json(self, row: pd.Series, stage: int = 1) -> Dict[str, Any]:
        """创建单个议员的JSON数据结构"""
        # 计算年龄
        votes_path = os.path.join(BASE_DIR, "data", "howTheyVoteDataSet", "votes.csv")
        df_votes = pd.read_csv(votes_path)
        row_votes = df_votes[df_votes["id"] == self.vote_id].iloc[0]
        timestamp = pd.to_datetime(row_votes["timestamp"])
        birthdate = pd.to_datetime(row["date_of_birth"])
        age = calculate_age(birthdate, timestamp)
        
        # 生成persona描述
        persona_description = self._create_persona_description(row, age)
        
        agent_json = {
            "agent_id": str(row["member_id"]),
            "vote_id": self.vote_id,
            "static_profile": {
                "basic_info": {
                    "member_id": int(row["member_id"]),
                    "fullname": row["fullname"],
                    "gender": row["gender"],
                    "date_of_birth": row["date_of_birth"],
                    "age": age,
                    "birthplace": row["birthplace"],
                    "country": row["country"],
                    "country_code": row["country_code"]
                },
                "political_identity": {
                    "group_code": row["group_code"],
                    "group_name": row["group_name"],
                    "national_party": row["national_party"],
                    "term": int(row["term"]),
                    "start_date": row["start_date"].strftime('%Y-%m-%d'),
                    "end_date": row["end_date"].strftime('%Y-%m-%d')
                },
                "persona_description": persona_description
            },
            "dynamic_state": {
                "current_voting_intention": {
                    "FOR": 0.33,
                    "AGAINST": 0.33,
                    "ABSTENTION": 0.34
                },
                "stance_confidence": 0.5,
                "last_updated": datetime.now().isoformat(),
                "update_history": []
            },
            "memory_bank": {
                "received_arguments": {
                    "pro_arguments": [],
                    "contra_arguments": [],
                    "neutral_arguments": []
                },
                "party_group_communications": [],
                "public_debate_inputs": [],
                "influential_speakers": []
            },
            "cognitive_outputs": {
                "initial_stance_analysis": {
                    "reasoning": "",
                    "key_considerations": [],
                    "generated_at": None
                },
                "generated_arguments": {
                    "own_pro_arguments": [],
                    "own_contra_arguments": [],
                    "generated_at": None
                },
                "stance_updates": []
            },
            "interaction_history": {
                "stage_1_initial": {
                    "completed": False,
                    "llm_interactions": [],
                    "final_voting_intention": None,
                    "final_stance_confidence": None
                },
                "stage_2_party_meeting": {
                    "completed": False,
                    "received_party_positions": [],
                    "party_leader_statements": [],
                    "llm_interactions": [],
                    "final_voting_intention": None,
                    "final_stance_confidence": None
                },
                "stage_2_public_debate": {
                    "completed": False,
                    "received_public_arguments": [],
                    "speaker_influences": [],
                    "llm_interactions": [],
                    "final_voting_intention": None,
                    "final_stance_confidence": None
                },
                "stage_3_final": {
                    "completed": False,
                    "final_vote": None,
                    "final_reasoning": None
                }
            },
            "simulation_metadata": {
                "proposal_info": {
                    "vote_title": self.vote_title,
                    "debate_speeches": self.speeches[:5]  # 只取前5个发言作为背景
                },
                "actual_vote": self.actual_votes.get(row["member_id"]),
                "created_at": datetime.now().isoformat(),
                "last_modified": datetime.now().isoformat(),
                "simulation_version": "1.0"
            }
        }
        
        return agent_json
    
    def _create_persona_description(self, row: pd.Series, age: int) -> str:
        """创建persona描述"""
        gender_call = "He" if row["gender"] == "male" else "She"
        
        national_party_sentence = ""
        if row["national_party"] == "Independent":
            national_party_sentence = f"{gender_call} is not part of a national party. "
        else:
            national_party_sentence = f"{gender_call} is part of the national party {row['national_party']}. "
        
        group_sentence = f"{gender_call} is part of "
        if row["group_name"] != "The Left group in the European Parliament - GUE/NGL":
            group_sentence += "the "
        group_sentence += f"{row['group_name']}."
        
        return f"{row['fullname']}, a {row['gender']} politician, {age} years old, born in {row['birthplace']}. {gender_call} is representing the country {row['country']}. {national_party_sentence}{group_sentence}"
    
    def _initialize_party_group_files(self):
        """初始化党团JSON文件"""
        for group_code, group_df in self.party_groups.items():
            group_data = {
                "group_info": {
                    "group_code": group_code,
                    "group_name": group_df.iloc[0]["group_name"],
                    "total_members": len(group_df),
                    "vote_id": self.vote_id,
                    "vote_title": self.vote_title
                },
                "voting_statistics": {
                    "stage_1": {"FOR": 0, "AGAINST": 0, "ABSTENTION": 0},
                    "stage_2": {"FOR": 0, "AGAINST": 0, "ABSTENTION": 0},
                    "stage_3": {"FOR": 0, "AGAINST": 0, "ABSTENTION": 0}
                },
                "agents": []
            }
            
            # 为每个议员创建agent JSON
            for _, row in group_df.iterrows():
                agent_json = self._create_agent_json(row)
                group_data["agents"].append(agent_json)
            
            # 保存党团文件
            group_file_path = os.path.join(self.proposal_dir, f"{group_code}.json")
            with open(group_file_path, 'w', encoding='utf-8') as f:
                json.dump(group_data, f, indent=2, ensure_ascii=False)

    def run_stage_1_initial_stance(self, temperature: float = 0.6) -> None:
        """阶段一：初始立场形成"""
        print("开始阶段一：初始立场形成")

        # 计算总议员数量用于进度条
        total_agents = sum(len(group_df) for group_df in self.party_groups.values())

        with tqdm(total=total_agents, desc="阶段一进度", unit="议员") as pbar:
            first_agent_processed = False

            for group_code in self.party_groups.keys():
                print(f"\n处理党团: {group_code}")
                group_file_path = os.path.join(self.proposal_dir, f"{group_code}.json")

                # 读取党团数据
                with open(group_file_path, 'r', encoding='utf-8') as f:
                    group_data = json.load(f)

                # 为每个议员生成初始立场
                for i, agent in enumerate(group_data["agents"]):
                    # 打印第一个议员的调试信息
                    if not first_agent_processed:
                        print(f"\n=== 调试信息：第一个议员的请求体 ===")
                        print(f"议员: {agent['static_profile']['basic_info']['fullname']}")
                        first_agent_processed = True

                    self._process_agent_stage_1(agent, temperature, debug=(i == 0 and group_code == list(self.party_groups.keys())[0]))
                    pbar.update(1)

                # 更新投票统计
                self._update_voting_statistics(group_data, stage=1)

                # 保存更新后的数据
                with open(group_file_path, 'w', encoding='utf-8') as f:
                    json.dump(group_data, f, indent=2, ensure_ascii=False)

        print("\n阶段一完成")

    def _process_agent_stage_1(self, agent: Dict[str, Any], temperature: float, debug: bool = False) -> None:
        """处理单个议员的阶段一交互"""
        # 创建prompt
        creator = PromptCreation()
        creator.create_persona(
            description=agent["static_profile"]["persona_description"],
            persona_call=PersonaCall.ACT,
            persona_call_freetext="the following politician."
        )

        # 任务指令
        task_instruction = f"""You are voting on the proposal: "{self.vote_title}".

Here are some debate speeches about this proposal:
{chr(10).join(agent["simulation_metadata"]["proposal_info"]["debate_speeches"][:3])}

Based on your political background and the information provided, form your initial stance on this proposal."""

        creator.set_task_instruction(task_instruction)
        creator.set_ouput_format_json(
            json_attributes=["reasoning", "key_considerations", "voting_intention", "stance_confidence"],
            json_explanation=[
                "Explain your reasoning for your stance on this proposal.",
                "List the key factors you considered in your decision.",
                "Your voting intention as probabilities: {\"FOR\": 0.0-1.0, \"AGAINST\": 0.0-1.0, \"ABSTENTION\": 0.0-1.0} (must sum to 1.0)",
                "Your confidence in this stance (0.0-1.0)"
            ]
        )

        full_prompt = creator.generate_prompt()
        system_message = "You are a helpful assistant that simulates European Parliament members' voting behavior."

        # 打印调试信息
        if debug:
            print(f"System Message: {system_message}")
            print(f"User Prompt: {full_prompt[:500]}...")

        # 调用LLM，增加重试机制
        try:
            max_retries = 3
            parsed_response = None
            response = ""

            for attempt in range(max_retries):
                try:
                    response = batch_generation([system_message], [full_prompt], max_new_tokens=1024, temperature=temperature)[0]

                    if debug and attempt == 0:
                        print(f"LLM Response: {response[:300]}...")

                    parsed_response = parse_json(response)

                    # 验证必需字段
                    if not self._validate_stage1_response(parsed_response):
                        if attempt < max_retries - 1:
                            print(f"响应验证失败，重试 {attempt + 1}/{max_retries}")
                            continue
                        else:
                            # 使用默认值
                            parsed_response = self._get_default_stage1_response()

                    break  # 成功则跳出重试循环

                except Exception as e:
                    error_type = type(e).__name__
                    error_msg = str(e)

                    # 详细错误分类
                    if "timeout" in error_msg.lower():
                        error_category = "网络超时"
                    elif "rate limit" in error_msg.lower() or "429" in error_msg:
                        error_category = "API速率限制"
                    elif "quota" in error_msg.lower() or "billing" in error_msg.lower():
                        error_category = "API配额问题"
                    elif "authentication" in error_msg.lower() or "401" in error_msg:
                        error_category = "认证失败"
                    elif "json" in error_msg.lower() or "parse" in error_msg.lower():
                        error_category = "JSON解析错误"
                    else:
                        error_category = "未知错误"

                    if attempt < max_retries - 1:
                        print(f"LLM调用失败 [{error_category}]，重试 {attempt + 1}/{max_retries}: {error_type}: {error_msg[:100]}")
                        # 如果是速率限制，等待更长时间
                        if "rate limit" in error_msg.lower():
                            import time
                            time.sleep(2)
                        continue
                    else:
                        print(f"LLM调用最终失败 [{error_category}]: {error_type}: {error_msg}")
                        parsed_response = self._get_default_stage1_response()
                        break

            # 更新agent数据
            agent["cognitive_outputs"]["initial_stance_analysis"]["reasoning"] = parsed_response.get("reasoning", "")
            agent["cognitive_outputs"]["initial_stance_analysis"]["key_considerations"] = parsed_response.get("key_considerations", [])
            agent["cognitive_outputs"]["initial_stance_analysis"]["generated_at"] = datetime.now().isoformat()

            # 更新投票意向
            voting_intention = parsed_response.get("voting_intention", {"FOR": 0.33, "AGAINST": 0.33, "ABSTENTION": 0.34})
            agent["dynamic_state"]["current_voting_intention"] = voting_intention
            agent["dynamic_state"]["stance_confidence"] = parsed_response.get("stance_confidence", 0.5)
            agent["dynamic_state"]["last_updated"] = datetime.now().isoformat()

            # 记录交互历史
            agent["interaction_history"]["stage_1_initial"]["llm_interactions"].append({
                "response": response,
                "parsed_output": parsed_response
            })
            agent["interaction_history"]["stage_1_initial"]["final_voting_intention"] = voting_intention
            agent["interaction_history"]["stage_1_initial"]["final_stance_confidence"] = parsed_response.get("stance_confidence", 0.5)
            agent["interaction_history"]["stage_1_initial"]["completed"] = True

            # 更新历史记录
            agent["dynamic_state"]["update_history"].append({
                "stage": "initial",
                "round": 1,
                "timestamp": datetime.now().isoformat(),
                "voting_intention": voting_intention,
                "stance_confidence": parsed_response.get("stance_confidence", 0.5),
                "trigger": "initial_stance_formation"
            })

        except Exception as e:
            print(f"处理议员 {agent['static_profile']['basic_info']['fullname']} 时出错: {e}")

    def run_stage_2_party_meeting(self, temperature: float = 0.6) -> None:
        """阶段二：党团会议"""
        print("开始阶段二：党团会议")

        # 计算总议员数量用于进度条
        total_agents = sum(len(group_df) for group_df in self.party_groups.values())

        with tqdm(total=total_agents, desc="阶段二进度", unit="议员") as pbar:
            first_agent_processed = False

            for group_code in self.party_groups.keys():
                print(f"\n处理党团会议: {group_code}")
                group_file_path = os.path.join(self.proposal_dir, f"{group_code}.json")

                # 读取党团数据
                with open(group_file_path, 'r', encoding='utf-8') as f:
                    group_data = json.load(f)

                # 分层抽样选择发言人
                speakers = self._select_party_speakers(group_data["agents"])

                # 打印党团发言人信息（仅第一个党团） - 注释掉以保持进度条清洁
                # if group_code == list(self.party_groups.keys())[0]:
                #     print(f"\n=== {group_code} 党团发言人详细信息 ===")
                #     print(f"党团总人数: {len(group_data['agents'])}")
                #     print(f"选出发言人数: {len(speakers)}")
                #     for i, speaker in enumerate(speakers):
                #         max_vote = max(speaker["dynamic_state"]["current_voting_intention"].keys(),
                #                      key=lambda k: speaker["dynamic_state"]["current_voting_intention"][k])
                #         confidence = speaker["dynamic_state"]["stance_confidence"]
                #         reasoning = speaker["cognitive_outputs"]["initial_stance_analysis"]["reasoning"][:150]
                #         print(f"  发言人 {i+1}: {speaker['static_profile']['basic_info']['fullname']}")
                #         print(f"    国家: {speaker['static_profile']['basic_info']['country_code']}")
                #         # 安全处理置信度格式化
                #         try:
                #             confidence_float = float(confidence)
                #             confidence_str = f"{confidence_float:.2f}"
                #         except (ValueError, TypeError):
                #             confidence_str = str(confidence)
                #
                #         print(f"    立场: {max_vote} (置信度: {confidence_str})")
                #         print(f"    推理: {reasoning}...")
                #         print(f"    投票分布: {speaker['dynamic_state']['current_voting_intention']}")
                #         print()

                # 为每个议员处理党团会议
                for i, agent in enumerate(group_data["agents"]):
                    # 打印第一个议员的调试信息
                    if not first_agent_processed:
                        print(f"\n=== 调试信息：第一个议员的党团会议请求体 ===")
                        print(f"议员: {agent['static_profile']['basic_info']['fullname']}")
                        first_agent_processed = True

                    self._process_agent_stage_2_party(agent, speakers, temperature, debug=(i == 0 and group_code == list(self.party_groups.keys())[0]))
                    pbar.update(1)

                # 更新投票统计
                self._update_voting_statistics(group_data, stage=2)

                # 保存更新后的数据
                with open(group_file_path, 'w', encoding='utf-8') as f:
                    json.dump(group_data, f, indent=2, ensure_ascii=False)

        print("\n阶段二党团会议完成")

    def _select_party_speakers(self, agents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按投票倾向进行分层抽样选择党团发言人

        分层抽样原则：
        1. 按照支持/反对/弃权的比例抽取5%作为发言人
        2. 保持抽样后的比例与原始样本比例一致
        3. 如果某个投票类型存在，则至少保证有一名发言人
        """
        # 按投票意向分组
        vote_groups = {"FOR": [], "AGAINST": [], "ABSTENTION": []}

        for agent in agents:
            voting_intention = agent["dynamic_state"]["current_voting_intention"]
            # 找到概率最高的选项
            max_vote = max(voting_intention.keys(), key=lambda k: voting_intention[k])
            vote_groups[max_vote].append(agent)

        total_agents = len(agents)
        target_speakers = max(1, int(total_agents * self.party_sampling_ratio))

        # 计算各组的原始比例
        group_proportions = {}
        for vote_type, group_agents in vote_groups.items():
            if group_agents:
                group_proportions[vote_type] = len(group_agents) / total_agents

        # print(f"    原始投票分布: FOR={len(vote_groups['FOR'])}, AGAINST={len(vote_groups['AGAINST'])}, ABSTENTION={len(vote_groups['ABSTENTION'])}")  # 注释掉调试信息

        speakers = []

        # 第一轮：按比例分配发言人数量
        allocated_speakers = {}
        remaining_speakers = target_speakers

        for vote_type, proportion in group_proportions.items():
            # 按比例计算应分配的发言人数
            expected_count = proportion * target_speakers
            allocated_count = int(expected_count)
            allocated_speakers[vote_type] = allocated_count
            remaining_speakers -= allocated_count

        # 第二轮：确保每个存在的投票类型至少有一名发言人
        for vote_type in group_proportions.keys():
            if allocated_speakers[vote_type] == 0:
                allocated_speakers[vote_type] = 1
                remaining_speakers -= 1

        # 第三轮：分配剩余的发言人名额（按比例分配给最大的组）
        if remaining_speakers > 0:
            # 找到人数最多的组
            largest_group = max(group_proportions.keys(), key=lambda k: len(vote_groups[k]))
            allocated_speakers[largest_group] += remaining_speakers

        # 实际抽样
        for vote_type, count in allocated_speakers.items():
            if count > 0 and vote_groups[vote_type]:
                selected = random.sample(vote_groups[vote_type], min(count, len(vote_groups[vote_type])))
                speakers.extend(selected)

        # 验证抽样结果
        sampled_distribution = {"FOR": 0, "AGAINST": 0, "ABSTENTION": 0}
        for speaker in speakers:
            voting_intention = speaker["dynamic_state"]["current_voting_intention"]
            max_vote = max(voting_intention.keys(), key=lambda k: voting_intention[k])
            sampled_distribution[max_vote] += 1

        # print(f"    发言人分布: FOR={sampled_distribution['FOR']}, AGAINST={sampled_distribution['AGAINST']}, ABSTENTION={sampled_distribution['ABSTENTION']}")  # 注释掉调试信息

        return speakers

    def _process_agent_stage_2_party(self, agent: Dict[str, Any], speakers: List[Dict[str, Any]], temperature: float, debug: bool = False) -> None:
        """处理单个议员的党团会议阶段"""
        # 准备党团成员信息
        party_info = []
        for speaker in speakers:
            if speaker["agent_id"] != agent["agent_id"]:  # 排除自己
                speaker_info = {
                    "name": speaker["static_profile"]["basic_info"]["fullname"],
                    "voting_intention": speaker["dynamic_state"]["current_voting_intention"],
                    "reasoning": speaker["cognitive_outputs"]["initial_stance_analysis"]["reasoning"]
                }
                party_info.append(speaker_info)

        # 创建prompt
        creator = PromptCreation()
        creator.create_persona(
            description=agent["static_profile"]["persona_description"],
            persona_call=PersonaCall.ACT,
            persona_call_freetext="the following politician."
        )

        # 构建党团信息文本，动态控制长度
        party_info_text = "Your party group members have shared their positions:\n"

        if party_info:
            # 根据采样比例动态调整prompt长度限制和推理长度
            num_speakers = len(party_info)

            # 基础长度计算
            base_prompt_parts = [
                f"Act as the following politician. {agent['static_profile']['persona_description']}",
                f"You are in a party group meeting discussing the proposal: \"{self.vote_title}\".",
                f"Your current position: {agent['dynamic_state']['current_voting_intention']}",
                f"Your reasoning: {agent['cognitive_outputs']['initial_stance_analysis']['reasoning']}"
            ]
            base_length = len("\n\n".join(base_prompt_parts))

            # 根据采样比例和发言人数量动态调整
            if self.party_sampling_ratio <= 0.05:  # 5%及以下
                max_prompt_length = 3000
                min_reasoning_length = 100
                max_reasoning_length = 250
            elif self.party_sampling_ratio <= 0.10:  # 5%-10%
                max_prompt_length = 4000
                min_reasoning_length = 80
                max_reasoning_length = 200
            elif self.party_sampling_ratio <= 0.20:  # 10%-20%
                max_prompt_length = 5000
                min_reasoning_length = 60
                max_reasoning_length = 150
            else:  # 20%以上
                max_prompt_length = 6000
                min_reasoning_length = 40
                max_reasoning_length = 100

            # 计算可用于发言人信息的字符数
            available_length = max_prompt_length - base_length - 300  # 预留300字符给其他内容

            if available_length > 0:
                # 计算每个发言人可分配的平均字符数
                chars_per_speaker = available_length // num_speakers

                # 动态调整推理长度
                reasoning_length = max(min_reasoning_length,
                                     min(max_reasoning_length, chars_per_speaker - 50))  # 减去50字符给姓名、立场等信息

                for info in party_info:
                    max_vote = max(info["voting_intention"].keys(), key=lambda k: info["voting_intention"][k])
                    truncated_reasoning = info['reasoning'][:reasoning_length]
                    party_info_text += f"- {info['name']}: Leaning towards {max_vote} (Reasoning: {truncated_reasoning}...)\n"
            else:
                # 如果空间不足，使用最小长度
                for info in party_info:
                    max_vote = max(info["voting_intention"].keys(), key=lambda k: info["voting_intention"][k])
                    truncated_reasoning = info['reasoning'][:min_reasoning_length]
                    party_info_text += f"- {info['name']}: Leaning towards {max_vote} (Reasoning: {truncated_reasoning}...)\n"
        else:
            party_info_text += "No party member information available.\n"

        task_instruction = f"""You are in a party group meeting discussing the proposal: "{self.vote_title}".

Your current position: {agent["dynamic_state"]["current_voting_intention"]}
Your reasoning: {agent["cognitive_outputs"]["initial_stance_analysis"]["reasoning"]}

{party_info_text}

Based on the party group discussion, reconsider your position. You may maintain or adjust your stance."""

        creator.set_task_instruction(task_instruction)
        creator.set_ouput_format_json(
            json_attributes=["party_influence", "updated_reasoning", "voting_intention", "stance_confidence"],
            json_explanation=[
                "How did the party group discussion influence your thinking?",
                "Your updated reasoning considering party input.",
                "Your updated voting intention as probabilities: {\"FOR\": 0.0-1.0, \"AGAINST\": 0.0-1.0, \"ABSTENTION\": 0.0-1.0}",
                "Your confidence in this updated stance (0.0-1.0)"
            ]
        )

        full_prompt = creator.generate_prompt()
        system_message = "You are a helpful assistant that simulates European Parliament members' voting behavior."

        # 打印调试信息
        if debug:
            print(f"System Message: {system_message}")
            print(f"\n=== 阶段二：第一个议员的完整请求体 ===")
            print(f"议员: {agent['static_profile']['basic_info']['fullname']}")
            print(f"党团: {agent['static_profile']['political_identity']['group_code']}")
            print(f"完整User Prompt:")
            print("-" * 80)
            print(full_prompt)
            print("-" * 80)

            print(f"\n=== 党团发言人详细信息 ===")
            print(f"党团发言人数量: {len(speakers)}")
            for i, speaker in enumerate(speakers):
                if speaker["agent_id"] != agent["agent_id"]:  # 排除自己
                    max_vote = max(speaker["dynamic_state"]["current_voting_intention"].keys(),
                                 key=lambda k: speaker["dynamic_state"]["current_voting_intention"][k])
                    confidence = speaker["dynamic_state"]["stance_confidence"]
                    reasoning = speaker["cognitive_outputs"]["initial_stance_analysis"]["reasoning"][:150]
                    print(f"  发言人 {i+1}: {speaker['static_profile']['basic_info']['fullname']}")

                    # 安全处理置信度格式化
                    try:
                        confidence_float = float(confidence)
                        confidence_str = f"{confidence_float:.2f}"
                    except (ValueError, TypeError):
                        confidence_str = str(confidence)

                    print(f"    立场: {max_vote} (置信度: {confidence_str})")
                    print(f"    推理: {reasoning}...")
                    print(f"    投票分布: {speaker['dynamic_state']['current_voting_intention']}")
                    print()

        # 调用LLM，增加重试机制
        try:
            max_retries = 3
            parsed_response = None
            response = ""

            for attempt in range(max_retries):
                try:
                    response = batch_generation([system_message], [full_prompt], max_new_tokens=1024, temperature=temperature)[0]

                    if debug and attempt == 0:
                        print(f"LLM Response: {response[:300]}...")

                    parsed_response = parse_json(response)

                    # 验证必需字段
                    if not self._validate_stage2_response(parsed_response):
                        if attempt < max_retries - 1:
                            print(f"响应验证失败，重试 {attempt + 1}/{max_retries}")
                            continue
                        else:
                            parsed_response = self._get_default_stage2_response(agent)

                    break

                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"LLM调用失败，重试 {attempt + 1}/{max_retries}: {e}")
                        continue
                    else:
                        print(f"LLM调用最终失败: {e}")
                        parsed_response = self._get_default_stage2_response(agent)
                        break

            # 更新agent数据
            voting_intention = parsed_response.get("voting_intention", agent["dynamic_state"]["current_voting_intention"])
            agent["dynamic_state"]["current_voting_intention"] = voting_intention
            agent["dynamic_state"]["stance_confidence"] = parsed_response.get("stance_confidence", 0.5)
            agent["dynamic_state"]["last_updated"] = datetime.now().isoformat()

            # 记录党团沟通
            agent["memory_bank"]["party_group_communications"] = [
                {
                    "member_id": speaker["agent_id"],
                    "voting_intention": speaker["dynamic_state"]["current_voting_intention"],
                    "message": speaker["cognitive_outputs"]["initial_stance_analysis"]["reasoning"][:200]
                }
                for speaker in speakers if speaker["agent_id"] != agent["agent_id"]
            ]

            # 记录交互历史
            agent["interaction_history"]["stage_2_party_meeting"]["received_party_positions"] = [
                {
                    "member_id": speaker["agent_id"],
                    "voting_intention": speaker["dynamic_state"]["current_voting_intention"],
                    "confidence": speaker["dynamic_state"]["stance_confidence"]
                }
                for speaker in speakers if speaker["agent_id"] != agent["agent_id"]
            ]

            agent["interaction_history"]["stage_2_party_meeting"]["llm_interactions"].append({
                "response": response,
                "parsed_output": parsed_response
            })
            agent["interaction_history"]["stage_2_party_meeting"]["final_voting_intention"] = voting_intention
            agent["interaction_history"]["stage_2_party_meeting"]["final_stance_confidence"] = parsed_response.get("stance_confidence", 0.5)
            agent["interaction_history"]["stage_2_party_meeting"]["completed"] = True

            # 更新历史记录
            agent["dynamic_state"]["update_history"].append({
                "stage": "party_meeting",
                "round": 2,
                "timestamp": datetime.now().isoformat(),
                "voting_intention": voting_intention,
                "stance_confidence": parsed_response.get("stance_confidence", 0.5),
                "trigger": "party_group_influence"
            })

        except Exception as e:
            print(f"处理议员 {agent['static_profile']['basic_info']['fullname']} 党团会议时出错: {e}")

    def run_stage_3_public_debate(self, temperature: float = 0.6) -> None:
        """阶段三：公开辩论"""
        print("开始阶段三：公开辩论")

        # 从所有党团中选择公开辩论发言人
        all_speakers = self._select_public_speakers()

        # 打印公开辩论发言人总体信息 - 注释掉以保持进度条清洁
        # print(f"\n=== 公开辩论发言人总体信息 ===")
        # print(f"总发言人数: {len(all_speakers)}")
        # speaker_by_group = {}
        # for speaker in all_speakers:
        #     group = speaker["static_profile"]["political_identity"]["group_code"]
        #     if group not in speaker_by_group:
        #         speaker_by_group[group] = []
        #     speaker_by_group[group].append(speaker)
        #
        # for group, speakers in speaker_by_group.items():
        #     print(f"  {group} 党团: {len(speakers)} 名发言人")
        #     for speaker in speakers:
        #         max_vote = max(speaker["dynamic_state"]["current_voting_intention"].keys(),
        #                      key=lambda k: speaker["dynamic_state"]["current_voting_intention"][k])
        #         print(f"    - {speaker['static_profile']['basic_info']['fullname']} ({speaker['static_profile']['basic_info']['country_code']}) -> {max_vote}")
        # print()

        # 计算总议员数量用于进度条
        total_agents = sum(len(group_df) for group_df in self.party_groups.values())

        with tqdm(total=total_agents, desc="阶段三进度", unit="议员") as pbar:
            first_agent_processed = False

            for group_code in self.party_groups.keys():
                print(f"\n处理公开辩论对党团 {group_code} 的影响")
                group_file_path = os.path.join(self.proposal_dir, f"{group_code}.json")

                # 读取党团数据
                with open(group_file_path, 'r', encoding='utf-8') as f:
                    group_data = json.load(f)

                # 为每个议员处理公开辩论
                for i, agent in enumerate(group_data["agents"]):
                    # 打印第一个议员的调试信息
                    if not first_agent_processed:
                        print(f"\n=== 调试信息：第一个议员的公开辩论请求体 ===")
                        print(f"议员: {agent['static_profile']['basic_info']['fullname']}")
                        first_agent_processed = True

                    self._process_agent_stage_3_public(agent, all_speakers, temperature, debug=(i == 0 and group_code == list(self.party_groups.keys())[0]))
                    pbar.update(1)

                # 更新投票统计
                self._update_voting_statistics(group_data, stage=3)

                # 保存更新后的数据
                with open(group_file_path, 'w', encoding='utf-8') as f:
                    json.dump(group_data, f, indent=2, ensure_ascii=False)

        print("\n阶段三公开辩论完成")

    def _select_public_speakers(self) -> List[Dict[str, Any]]:
        """按党团人数比例选择公开辩论发言人"""
        all_speakers = []

        for group_code, group_df in self.party_groups.items():
            group_file_path = os.path.join(self.proposal_dir, f"{group_code}.json")
            with open(group_file_path, 'r', encoding='utf-8') as f:
                group_data = json.load(f)

            # 按党团大小确定发言人数量
            group_size = len(group_data["agents"])
            num_speakers = max(1, int(group_size * self.public_sampling_ratio))

            # 随机选择发言人
            selected_speakers = random.sample(group_data["agents"], min(num_speakers, group_size))
            all_speakers.extend(selected_speakers)

        return all_speakers

    def _get_latest_reasoning(self, agent: Dict[str, Any]) -> str:
        """获取议员的最新推理内容"""
        # 尝试从阶段二党团会议获取
        stage_2_interactions = agent.get("interaction_history", {}).get("stage_2_party_meeting", {}).get("llm_interactions", [])
        if stage_2_interactions:
            latest_interaction = stage_2_interactions[-1]
            reasoning = latest_interaction.get("parsed_output", {}).get("updated_reasoning", "")
            if reasoning:
                return reasoning

        # 如果阶段二没有，则从初始立场分析获取
        initial_reasoning = agent.get("cognitive_outputs", {}).get("initial_stance_analysis", {}).get("reasoning", "")
        return initial_reasoning[:200] if initial_reasoning else "No reasoning available"

    def _validate_stage1_response(self, response: dict) -> bool:
        """验证阶段一响应的完整性"""
        if not response:
            return False

        required_fields = ["reasoning", "voting_intention", "stance_confidence"]
        for field in required_fields:
            if field not in response:
                return False

        # 验证voting_intention格式
        voting_intention = response.get("voting_intention", {})
        if not isinstance(voting_intention, dict):
            return False

        required_votes = ["FOR", "AGAINST", "ABSTENTION"]
        for vote in required_votes:
            if vote not in voting_intention:
                return False
            try:
                float(voting_intention[vote])
            except (ValueError, TypeError):
                return False

        # 验证stance_confidence
        try:
            confidence = float(response.get("stance_confidence", 0))
            if not (0 <= confidence <= 1):
                return False
        except (ValueError, TypeError):
            return False

        return True

    def _get_default_stage1_response(self) -> dict:
        """获取默认的阶段一响应"""
        return {
            "reasoning": "Unable to generate proper reasoning due to technical issues.",
            "key_considerations": ["Technical error occurred"],
            "voting_intention": {
                "FOR": 0.33,
                "AGAINST": 0.33,
                "ABSTENTION": 0.34
            },
            "stance_confidence": 0.5
        }

    def _validate_stage2_response(self, response: dict) -> bool:
        """验证阶段二响应的完整性"""
        if not response:
            return False

        required_fields = ["voting_intention", "stance_confidence"]
        for field in required_fields:
            if field not in response:
                return False

        # 验证voting_intention格式
        voting_intention = response.get("voting_intention", {})
        if not isinstance(voting_intention, dict):
            return False

        required_votes = ["FOR", "AGAINST", "ABSTENTION"]
        for vote in required_votes:
            if vote not in voting_intention:
                return False
            try:
                float(voting_intention[vote])
            except (ValueError, TypeError):
                return False

        # 验证stance_confidence
        try:
            confidence = float(response.get("stance_confidence", 0))
            if not (0 <= confidence <= 1):
                return False
        except (ValueError, TypeError):
            return False

        return True

    def _get_default_stage2_response(self, agent: Dict[str, Any]) -> dict:
        """获取默认的阶段二响应"""
        # 保持当前投票意向不变
        current_intention = agent["dynamic_state"]["current_voting_intention"]
        return {
            "party_influence": "Unable to process party influence due to technical issues.",
            "updated_reasoning": "Technical error occurred during party meeting processing.",
            "voting_intention": current_intention,
            "stance_confidence": agent["dynamic_state"]["stance_confidence"]
        }

    def _validate_stage3_response(self, response: dict) -> bool:
        """验证阶段三响应的完整性"""
        if not response:
            return False

        required_fields = ["final_vote", "final_reasoning"]
        for field in required_fields:
            if field not in response:
                return False

        # 验证final_vote
        final_vote = response.get("final_vote")
        if final_vote not in VOTE_OPTIONS:
            return False

        return True

    def _get_default_stage3_response(self) -> dict:
        """获取默认的阶段三响应"""
        return {
            "public_debate_influence": "Unable to process public debate influence due to technical issues.",
            "final_reasoning": "Technical error occurred during final decision processing.",
            "final_vote": "ABSTENTION",
            "stance_confidence": 0.5
        }

    def _process_agent_stage_3_public(self, agent: Dict[str, Any], all_speakers: List[Dict[str, Any]], temperature: float, debug: bool = False) -> None:
        """处理单个议员的公开辩论阶段"""
        # 准备公开辩论信息
        public_arguments = []
        for speaker in all_speakers:
            if speaker["agent_id"] != agent["agent_id"]:  # 排除自己
                speaker_info = {
                    "name": speaker["static_profile"]["basic_info"]["fullname"],
                    "group": speaker["static_profile"]["political_identity"]["group_code"],
                    "voting_intention": speaker["dynamic_state"]["current_voting_intention"],
                    "reasoning": self._get_latest_reasoning(speaker)
                }
                public_arguments.append(speaker_info)

        # 创建prompt
        creator = PromptCreation()
        creator.create_persona(
            description=agent["static_profile"]["persona_description"],
            persona_call=PersonaCall.ACT,
            persona_call_freetext="the following politician."
        )

        # 构建公开辩论信息文本，动态控制长度
        public_info_text = "Public debate speakers from different party groups:\n"

        if public_arguments:
            # 根据采样比例动态调整prompt长度限制和推理长度
            num_speakers = len(public_arguments)

            # 基础长度计算
            base_prompt_parts = [
                f"Act as the following politician. {agent['static_profile']['persona_description']}",
                f"You are in the final public debate on the proposal: \"{self.vote_title}\".",
                f"Your current position after party meeting: {agent['dynamic_state']['current_voting_intention']}",
                f"Your reasoning: {self._get_latest_reasoning(agent)}"
            ]
            base_length = len("\n\n".join(base_prompt_parts))

            # 根据采样比例和发言人数量动态调整
            if self.public_sampling_ratio <= 0.05:  # 5%及以下
                max_prompt_length = 4000
                min_reasoning_length = 80
                max_reasoning_length = 200
            elif self.public_sampling_ratio <= 0.10:  # 5%-10%
                max_prompt_length = 5000
                min_reasoning_length = 60
                max_reasoning_length = 150
            elif self.public_sampling_ratio <= 0.20:  # 10%-20%
                max_prompt_length = 6000
                min_reasoning_length = 40
                max_reasoning_length = 120
            else:  # 20%以上
                max_prompt_length = 8000
                min_reasoning_length = 30
                max_reasoning_length = 80

            # 计算可用于发言人信息的字符数
            available_length = max_prompt_length - base_length - 400  # 预留400字符给其他内容

            if available_length > 0:
                # 计算每个发言人可分配的平均字符数
                chars_per_speaker = available_length // num_speakers

                # 动态调整推理长度
                reasoning_length = max(min_reasoning_length,
                                     min(max_reasoning_length, chars_per_speaker - 60))  # 减去60字符给姓名、党团等信息

                for info in public_arguments:
                    max_vote = max(info["voting_intention"].keys(), key=lambda k: info["voting_intention"][k])
                    truncated_reasoning = info['reasoning'][:reasoning_length]
                    public_info_text += f"- {info['name']} ({info['group']}): {max_vote} - {truncated_reasoning}...\n"
            else:
                # 如果空间不足，使用最小长度
                for info in public_arguments:
                    max_vote = max(info["voting_intention"].keys(), key=lambda k: info["voting_intention"][k])
                    truncated_reasoning = info['reasoning'][:min_reasoning_length]
                    public_info_text += f"- {info['name']} ({info['group']}): {max_vote} - {truncated_reasoning}...\n"
        else:
            public_info_text += "No public debate information available.\n"

        task_instruction = f"""You are participating in the public debate on: "{self.vote_title}".

Your current position after party meeting: {agent["dynamic_state"]["current_voting_intention"]}

{public_info_text}

After hearing these diverse perspectives from across party lines, make your final decision."""

        creator.set_task_instruction(task_instruction)
        creator.set_ouput_format_json(
            json_attributes=["public_debate_influence", "final_reasoning", "final_vote", "stance_confidence"],
            json_explanation=[
                "How did the public debate influence your final decision?",
                "Your final reasoning for your vote.",
                "Your final vote choice: FOR, AGAINST, or ABSTENTION",
                "Your final confidence in this decision (0.0-1.0)"
            ]
        )

        full_prompt = creator.generate_prompt()
        system_message = "You are a helpful assistant that simulates European Parliament members' voting behavior."

        # 打印调试信息
        if debug:
            print(f"System Message: {system_message}")
            print(f"\n=== 阶段三：第一个议员的完整请求体 ===")
            print(f"议员: {agent['static_profile']['basic_info']['fullname']}")
            print(f"党团: {agent['static_profile']['political_identity']['group_code']}")
            print(f"完整User Prompt:")
            print("-" * 80)
            print(full_prompt)
            print("-" * 80)

            print(f"\n=== 公开辩论发言人详细信息 ===")
            print(f"公开辩论发言人数量: {len(all_speakers)}")
            print(f"影响当前议员的发言人数量: {len(public_arguments)}")
            for i, info in enumerate(public_arguments):  # 显示所有发言人
                max_vote = max(info["voting_intention"].keys(), key=lambda k: info["voting_intention"][k])
                confidence = max(info["voting_intention"].values())
                print(f"  发言人 {i+1}: {info['name']}")
                print(f"    党团: {info['group']}")

                # 安全处理置信度格式化
                try:
                    confidence_float = float(confidence)
                    confidence_str = f"{confidence_float:.2f}"
                except (ValueError, TypeError):
                    confidence_str = str(confidence)

                print(f"    上一轮立场: {max_vote} (概率: {confidence_str})")
                print(f"    上一轮推理: {info['reasoning'][:150]}...")
                print(f"    投票分布: {info['voting_intention']}")
                print()

        # 调用LLM，增加重试机制
        try:
            max_retries = 3
            parsed_response = None
            response = ""

            for attempt in range(max_retries):
                try:
                    response = batch_generation([system_message], [full_prompt], max_new_tokens=1024, temperature=temperature)[0]

                    if debug and attempt == 0:
                        print(f"LLM Response: {response[:300]}...")

                    parsed_response = parse_json(response)

                    # 验证必需字段
                    if not self._validate_stage3_response(parsed_response):
                        if attempt < max_retries - 1:
                            print(f"响应验证失败，重试 {attempt + 1}/{max_retries}")
                            continue
                        else:
                            parsed_response = self._get_default_stage3_response()

                    break

                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"LLM调用失败，重试 {attempt + 1}/{max_retries}: {e}")
                        continue
                    else:
                        print(f"LLM调用最终失败: {e}")
                        parsed_response = self._get_default_stage3_response()
                        break

            # 更新最终投票
            final_vote = parsed_response.get("final_vote", "ABSTENTION")
            if final_vote not in VOTE_OPTIONS:
                final_vote = "ABSTENTION"

            # 更新agent数据
            agent["interaction_history"]["stage_3_final"]["final_vote"] = final_vote
            agent["interaction_history"]["stage_3_final"]["final_reasoning"] = parsed_response.get("final_reasoning", "")
            agent["interaction_history"]["stage_3_final"]["completed"] = True

            # 记录公开辩论输入
            agent["memory_bank"]["public_debate_inputs"] = [
                {
                    "speaker_id": speaker["agent_id"],
                    "speaker_group": speaker["static_profile"]["political_identity"]["group_code"],
                    "argument_type": max(speaker["dynamic_state"]["current_voting_intention"].keys(),
                                       key=lambda k: speaker["dynamic_state"]["current_voting_intention"][k]),
                    "content": self._get_latest_reasoning(speaker)[:200]
                }
                for speaker in all_speakers if speaker["agent_id"] != agent["agent_id"]
            ]

            agent["interaction_history"]["stage_2_public_debate"]["llm_interactions"].append({
                "response": response,
                "parsed_output": parsed_response
            })
            agent["interaction_history"]["stage_2_public_debate"]["completed"] = True

        except Exception as e:
            print(f"处理议员 {agent['static_profile']['basic_info']['fullname']} 公开辩论时出错: {e}")

    def _update_voting_statistics(self, group_data: Dict[str, Any], stage: int) -> None:
        """更新党团投票统计"""
        vote_counts = {"FOR": 0, "AGAINST": 0, "ABSTENTION": 0}

        for agent in group_data["agents"]:
            if stage == 1:
                voting_intention = agent["dynamic_state"]["current_voting_intention"]
                max_vote = max(voting_intention.keys(), key=lambda k: voting_intention[k])
                vote_counts[max_vote] += 1
            elif stage == 2:
                if agent["interaction_history"]["stage_2_party_meeting"]["completed"]:
                    voting_intention = agent["interaction_history"]["stage_2_party_meeting"]["final_voting_intention"]
                    if voting_intention:
                        max_vote = max(voting_intention.keys(), key=lambda k: voting_intention[k])
                        vote_counts[max_vote] += 1
            elif stage == 3:
                if agent["interaction_history"]["stage_3_final"]["completed"]:
                    final_vote = agent["interaction_history"]["stage_3_final"]["final_vote"]
                    if final_vote in vote_counts:
                        vote_counts[final_vote] += 1

        group_data["voting_statistics"][f"stage_{stage}"] = vote_counts

    def generate_final_csv(self, output_path: str = None) -> pd.DataFrame:
        """生成最终的CSV结果文件"""
        if output_path is None:
            output_path = os.path.join(self.proposal_dir, f"final_results_{self.vote_id}.csv")

        results = []

        for group_code in self.party_groups.keys():
            group_file_path = os.path.join(self.proposal_dir, f"{group_code}.json")
            with open(group_file_path, 'r', encoding='utf-8') as f:
                group_data = json.load(f)

            for agent in group_data["agents"]:
                # 收集三个阶段的投票结果
                stage_votes = []
                stage_reasonings = []

                # 阶段一：初始立场（转换为最可能的投票选择）
                stage1_intention = agent["dynamic_state"]["current_voting_intention"]
                stage1_vote = max(stage1_intention.keys(), key=lambda k: stage1_intention[k])
                stage_votes.append(stage1_vote)
                stage_reasonings.append(agent["cognitive_outputs"]["initial_stance_analysis"]["reasoning"])

                # 阶段二：党团会议后的立场
                stage2_interactions = agent.get("interaction_history", {}).get("stage_2_party_meeting", {}).get("llm_interactions", [])
                if stage2_interactions:
                    stage2_intention = stage2_interactions[-1].get("parsed_output", {}).get("voting_intention", stage1_intention)
                    stage2_vote = max(stage2_intention.keys(), key=lambda k: stage2_intention[k])
                    stage2_reasoning = stage2_interactions[-1].get("parsed_output", {}).get("updated_reasoning", "")
                else:
                    stage2_vote = stage1_vote
                    stage2_reasoning = "No party meeting interaction recorded"
                stage_votes.append(stage2_vote)
                stage_reasonings.append(stage2_reasoning)

                # 阶段三：最终投票
                stage3_vote = agent.get("interaction_history", {}).get("stage_3_final", {}).get("final_vote", "ABSTENTION")
                stage3_reasoning = agent.get("interaction_history", {}).get("stage_3_final", {}).get("final_reasoning", "")
                stage_votes.append(stage3_vote)
                stage_reasonings.append(stage3_reasoning)

                result_row = {
                    "member_id": agent["static_profile"]["basic_info"]["member_id"],
                    "vote_id": self.vote_id,
                    "fullName": agent["static_profile"]["basic_info"]["fullname"],
                    "reasonings": stage_reasonings,  # 三个阶段的推理列表
                    "llm_votes": stage_votes,        # 三个阶段的投票列表
                    "actual_vote": agent["simulation_metadata"]["actual_vote"],
                    "group": agent["static_profile"]["political_identity"]["group_code"],
                    "country": agent["static_profile"]["basic_info"]["country_code"]
                }
                results.append(result_row)

        df_results = pd.DataFrame(results)
        df_results.to_csv(output_path, index=False)
        print(f"最终结果已保存到: {output_path}")

        return df_results

    def run_full_simulation(self, temperature: float = 0.6) -> pd.DataFrame:
        """运行完整的三阶段模拟"""
        print(f"开始完整模拟 - 提案ID: {self.vote_id}")

        # 阶段一：初始立场形成
        self.run_stage_1_initial_stance(temperature)

        # 阶段二：党团会议
        self.run_stage_2_party_meeting(temperature)

        # 阶段三：公开辩论
        self.run_stage_3_public_debate(temperature)

        # 生成最终结果
        results = self.generate_final_csv()

        print("完整模拟结束")
        return results
