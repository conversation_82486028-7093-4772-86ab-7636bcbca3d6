#!/usr/bin/env python3
"""
测试多阶段模拟系统
"""

import sys
import os
sys.path.append('src')

from multi_stage_simulation import MultiStageSimulation
import pandas as pd

# 统一数据根路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

def test_data_loading():
    """测试数据加载功能"""
    print("测试数据加载...")
    
    # 获取一个可用的投票ID
    votes_path = os.path.join(BASE_DIR, "data", "howTheyVoteDataSet", "votes.csv")
    speeches_path = os.path.join(BASE_DIR, "data", "debates", "counterfactual_speeches.csv")
    
    votes = pd.read_csv(votes_path)
    speeches = pd.read_csv(speeches_path)
    
    # 筛选2024年后的投票
    votes['timestamp'] = pd.to_datetime(votes['timestamp'], errors='coerce')
    cutoff_date = pd.Timestamp('2024-01-01')
    votes = votes[votes['timestamp'] >= cutoff_date]
    
    # 取交集
    vote_ids = set(votes['id'])
    speech_ids = set(speeches['id'])
    common_ids = vote_ids & speech_ids
    
    if not common_ids:
        print("❌ 没有找到可用的投票数据")
        return None
    
    # 选择第一个可用的投票ID
    test_vote_id = list(common_ids)[0]
    test_vote_title = votes[votes['id'] == test_vote_id].iloc[0]['display_title']
    
    print(f"✅ 找到测试投票: ID={test_vote_id}, 标题={test_vote_title}")
    return test_vote_id, test_vote_title

def test_simulation_initialization():
    """测试模拟系统初始化"""
    print("\n测试模拟系统初始化...")
    
    vote_data = test_data_loading()
    if not vote_data:
        return False
    
    test_vote_id, test_vote_title = vote_data
    
    try:
        simulation = MultiStageSimulation(
            vote_id=test_vote_id,
            vote_title=test_vote_title,
            output_dir="./test_output"
        )
        
        print(f"✅ 模拟系统初始化成功")
        print(f"   - 投票ID: {simulation.vote_id}")
        print(f"   - 投票标题: {simulation.vote_title}")
        print(f"   - 输出目录: {simulation.proposal_dir}")
        print(f"   - 党团数量: {len(simulation.party_groups)}")
        
        # 打印党团信息
        for group_code, group_df in simulation.party_groups.items():
            print(f"   - {group_code}: {len(group_df)} 名议员")
        
        return simulation
        
    except Exception as e:
        print(f"❌ 模拟系统初始化失败: {e}")
        return False

def test_stage_1():
    """测试阶段一"""
    print("\n测试阶段一（初始立场形成）...")
    
    simulation = test_simulation_initialization()
    if not simulation:
        return False
    
    try:
        # 只处理一个小党团进行测试
        test_group = list(simulation.party_groups.keys())[0]
        print(f"测试党团: {test_group}")
        
        # 临时修改党团数据，只保留少数议员进行测试
        original_members = simulation.party_groups[test_group].copy()
        simulation.party_groups[test_group] = original_members.head(2)  # 只测试前2个议员
        
        # 重新初始化文件
        simulation._initialize_party_group_files()
        
        # 运行阶段一
        simulation.run_stage_1_initial_stance(temperature=0.6)
        
        print("✅ 阶段一测试完成")
        return simulation
        
    except Exception as e:
        print(f"❌ 阶段一测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_structure():
    """测试JSON文件结构"""
    print("\n测试JSON文件结构...")
    
    simulation = test_simulation_initialization()
    if not simulation:
        return False
    
    try:
        # 检查生成的JSON文件
        import json
        
        for group_code in list(simulation.party_groups.keys())[:1]:  # 只检查第一个党团
            group_file_path = os.path.join(simulation.proposal_dir, f"{group_code}.json")
            
            if os.path.exists(group_file_path):
                with open(group_file_path, 'r', encoding='utf-8') as f:
                    group_data = json.load(f)
                
                print(f"✅ {group_code}.json 文件结构正确")
                print(f"   - 党团信息: {group_data['group_info']['group_name']}")
                print(f"   - 议员数量: {len(group_data['agents'])}")
                print(f"   - 投票统计: {group_data['voting_statistics']}")
                
                # 检查第一个议员的JSON结构
                if group_data['agents']:
                    agent = group_data['agents'][0]
                    print(f"   - 示例议员: {agent['static_profile']['basic_info']['fullname']}")
                    print(f"   - JSON结构完整性: ✅")
                
            else:
                print(f"❌ {group_code}.json 文件不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ JSON结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 多阶段模拟系统测试")
    print("=" * 50)
    
    # 测试数据加载
    if not test_data_loading():
        print("❌ 数据加载测试失败，退出")
        return
    
    # 测试JSON结构
    if not test_json_structure():
        print("❌ JSON结构测试失败，退出")
        return
    
    # 测试阶段一（注释掉LLM调用部分，避免实际API调用）
    print("\n⚠️  阶段一测试需要LLM API调用，跳过...")
    # if not test_stage_1():
    #     print("❌ 阶段一测试失败")
    #     return
    
    print("\n🎉 所有测试通过！")
    print("\n📝 使用说明:")
    print("1. 运行单个投票模拟:")
    print("   python src/main_multi_stage.py --single_vote 164499")
    print("\n2. 运行多个投票模拟:")
    print("   python src/main_multi_stage.py --vote_list 164499 164500")
    print("\n3. 运行所有可用投票:")
    print("   python src/main_multi_stage.py")

if __name__ == "__main__":
    main()
