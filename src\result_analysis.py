#!/usr/bin/env python3
"""
结果分析脚本：计算多阶段模拟的评估指标
包括F1分数、弃权票F1分数、党团凝聚力得分
"""

import pandas as pd
import numpy as np
import json
import os
from typing import Dict, List, Tuple, Any
from sklearn.metrics import f1_score, classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings('ignore')

class ResultAnalyzer:
    """结果分析器"""
    
    def __init__(self, results_csv_path: str, output_dir: str):
        """
        初始化分析器
        
        Args:
            results_csv_path: 结果CSV文件路径
            output_dir: 输出目录
        """
        self.results_csv_path = results_csv_path
        self.output_dir = output_dir
        self.df = pd.read_csv(results_csv_path)
        self.vote_options = ['FOR', 'AGAINST', 'ABSTENTION']
        
        # 解析llm_votes列（字符串格式的列表）
        self._parse_llm_votes()
        
    def _parse_llm_votes(self):
        """解析llm_votes列，将字符串转换为列表"""
        import ast
        
        def safe_eval(x):
            try:
                if isinstance(x, str):
                    return ast.literal_eval(x)
                return x
            except:
                return ['ABSTENTION', 'ABSTENTION', 'ABSTENTION']  # 默认值
        
        self.df['llm_votes_parsed'] = self.df['llm_votes'].apply(safe_eval)
        
        # 提取三个阶段的投票
        self.df['stage1_vote'] = self.df['llm_votes_parsed'].apply(lambda x: x[0] if len(x) > 0 else 'ABSTENTION')
        self.df['stage2_vote'] = self.df['llm_votes_parsed'].apply(lambda x: x[1] if len(x) > 1 else 'ABSTENTION')
        self.df['stage3_vote'] = self.df['llm_votes_parsed'].apply(lambda x: x[2] if len(x) > 2 else 'ABSTENTION')
        
    def calculate_accuracy_and_f1_scores(self) -> Dict[str, Any]:
        """计算准确率和F1分数"""
        results = {}
        
        # 为每个阶段计算F1分数
        for stage_num, stage_col in enumerate(['stage1_vote', 'stage2_vote', 'stage3_vote'], 1):
            stage_results = {}
            
            # 整体F1分数（宏平均和微平均）
            f1_macro = f1_score(self.df['actual_vote'], self.df[stage_col], 
                               labels=self.vote_options, average='macro', zero_division=0)
            f1_micro = f1_score(self.df['actual_vote'], self.df[stage_col], 
                               labels=self.vote_options, average='micro', zero_division=0)
            f1_weighted = f1_score(self.df['actual_vote'], self.df[stage_col], 
                                  labels=self.vote_options, average='weighted', zero_division=0)
            
            # 各类别的F1分数
            f1_per_class = f1_score(self.df['actual_vote'], self.df[stage_col],
                                   labels=self.vote_options, average=None, zero_division=0)

            # 准确率计算
            accuracy = accuracy_score(self.df['actual_vote'], self.df[stage_col])

            stage_results = {
                'accuracy': float(accuracy),
                'f1_macro': float(f1_macro),
                'f1_micro': float(f1_micro),
                'f1_weighted': float(f1_weighted),
                'f1_for': float(f1_per_class[0]),
                'f1_against': float(f1_per_class[1]),
                'f1_abstention': float(f1_per_class[2])
            }
            
            # 弃权票专门的F1分数
            abstention_f1 = self._calculate_abstention_f1(self.df['actual_vote'], self.df[stage_col])
            stage_results['abstention_f1_detailed'] = abstention_f1
            
            results[f'stage_{stage_num}'] = stage_results
            
        return results
    
    def _calculate_abstention_f1(self, y_true: pd.Series, y_pred: pd.Series) -> Dict[str, float]:
        """专门计算弃权票的F1分数"""
        # 将问题转换为二分类：弃权 vs 非弃权
        y_true_binary = (y_true == 'ABSTENTION').astype(int)
        y_pred_binary = (y_pred == 'ABSTENTION').astype(int)
        
        # 计算混淆矩阵
        tn = ((y_true_binary == 0) & (y_pred_binary == 0)).sum()
        tp = ((y_true_binary == 1) & (y_pred_binary == 1)).sum()
        fn = ((y_true_binary == 1) & (y_pred_binary == 0)).sum()
        fp = ((y_true_binary == 0) & (y_pred_binary == 1)).sum()
        
        # 计算精确率、召回率、F1分数
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1),
            'true_positives': int(tp),
            'true_negatives': int(tn),
            'false_positives': int(fp),
            'false_negatives': int(fn),
            'total_actual_abstentions': int((y_true == 'ABSTENTION').sum()),
            'total_predicted_abstentions': int((y_pred == 'ABSTENTION').sum())
        }
    
    def calculate_party_cohesion(self) -> Dict[str, Any]:
        """计算党团凝聚力得分"""
        cohesion_results = {}
        
        # 为每个阶段计算党团凝聚力
        for stage_num, stage_col in enumerate(['stage1_vote', 'stage2_vote', 'stage3_vote'], 1):
            stage_cohesion = {}
            
            # 实际投票的党团凝聚力
            actual_cohesion = self._calculate_cohesion_by_group(self.df, 'actual_vote')
            
            # 预测投票的党团凝聚力
            predicted_cohesion = self._calculate_cohesion_by_group(self.df, stage_col)
            
            stage_cohesion = {
                'actual_cohesion': actual_cohesion,
                'predicted_cohesion': predicted_cohesion,
                'cohesion_similarity': self._calculate_cohesion_similarity(actual_cohesion, predicted_cohesion)
            }
            
            cohesion_results[f'stage_{stage_num}'] = stage_cohesion
            
        return cohesion_results
    
    def _calculate_cohesion_by_group(self, df: pd.DataFrame, vote_col: str) -> Dict[str, Any]:
        """计算各党团的凝聚力"""
        group_cohesion = {}
        
        for group in df['group'].unique():
            group_df = df[df['group'] == group]
            if len(group_df) == 0:
                continue
                
            # 计算该党团内各投票选项的比例
            vote_counts = group_df[vote_col].value_counts()
            total_votes = len(group_df)
            
            # 计算多样性指数（基尼不纯度）
            gini_impurity = 1.0
            for vote_option in self.vote_options:
                proportion = vote_counts.get(vote_option, 0) / total_votes
                gini_impurity -= proportion ** 2
            
            # 凝聚力 = 1 - 多样性指数
            cohesion_score = 1.0 - gini_impurity
            
            # 计算主导投票选项及其比例
            if len(vote_counts) > 0:
                dominant_vote = vote_counts.index[0]
                dominant_proportion = vote_counts.iloc[0] / total_votes
            else:
                dominant_vote = 'ABSTENTION'
                dominant_proportion = 0.0
            
            group_cohesion[group] = {
                'cohesion_score': float(cohesion_score),
                'gini_impurity': float(gini_impurity),
                'dominant_vote': dominant_vote,
                'dominant_proportion': float(dominant_proportion),
                'total_members': int(total_votes),
                'vote_distribution': {vote: int(count) for vote, count in vote_counts.items()}
            }
        
        # 计算整体平均凝聚力
        if group_cohesion:
            avg_cohesion = np.mean([g['cohesion_score'] for g in group_cohesion.values()])
            weighted_avg_cohesion = np.average(
                [g['cohesion_score'] for g in group_cohesion.values()],
                weights=[g['total_members'] for g in group_cohesion.values()]
            )
        else:
            avg_cohesion = 0.0
            weighted_avg_cohesion = 0.0
        
        return {
            'by_group': group_cohesion,
            'average_cohesion': float(avg_cohesion),
            'weighted_average_cohesion': float(weighted_avg_cohesion)
        }
    
    def _calculate_cohesion_similarity(self, actual: Dict, predicted: Dict) -> Dict[str, float]:
        """计算实际和预测凝聚力的相似性"""
        actual_groups = actual['by_group']
        predicted_groups = predicted['by_group']
        
        similarities = {}
        
        for group in actual_groups.keys():
            if group in predicted_groups:
                actual_score = actual_groups[group]['cohesion_score']
                predicted_score = predicted_groups[group]['cohesion_score']
                
                # 计算绝对差异
                abs_diff = abs(actual_score - predicted_score)
                # 相似性 = 1 - 标准化差异
                similarity = 1.0 - abs_diff
                similarities[group] = float(similarity)
        
        # 计算整体相似性
        if similarities:
            overall_similarity = np.mean(list(similarities.values()))
        else:
            overall_similarity = 0.0
        
        return {
            'by_group': similarities,
            'overall_similarity': float(overall_similarity)
        }
    
    def generate_detailed_report(self) -> Dict[str, Any]:
        """生成详细的分析报告"""
        print("🔍 开始计算评估指标...")
        
        # 计算准确率和F1分数
        print("  - 计算准确率和F1分数...")
        f1_results = self.calculate_accuracy_and_f1_scores()
        
        # 计算党团凝聚力
        print("  - 计算党团凝聚力...")
        cohesion_results = self.calculate_party_cohesion()
        
        # 生成分类报告
        print("  - 生成分类报告...")
        classification_reports = {}
        for stage_num, stage_col in enumerate(['stage1_vote', 'stage2_vote', 'stage3_vote'], 1):
            report = classification_report(
                self.df['actual_vote'], 
                self.df[stage_col], 
                labels=self.vote_options,
                output_dict=True,
                zero_division=0
            )
            classification_reports[f'stage_{stage_num}'] = report
        
        # 汇总结果
        final_report = {
            'metadata': {
                'total_members': len(self.df),
                'total_groups': self.df['group'].nunique(),
                'vote_options': self.vote_options,
                'analysis_timestamp': pd.Timestamp.now().isoformat()
            },
            'f1_scores': f1_results,
            'party_cohesion': cohesion_results,
            'classification_reports': classification_reports
        }
        
        return final_report
    
    def save_results(self, report: Dict[str, Any]):
        """保存分析结果"""
        # 保存完整的JSON报告
        json_path = os.path.join(self.output_dir, 'analysis_report.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 保存简化的CSV摘要
        summary_data = []
        for stage in ['stage_1', 'stage_2', 'stage_3']:
            f1_data = report['f1_scores'][stage]
            cohesion_data = report['party_cohesion'][stage]
            
            summary_data.append({
                'stage': stage,
                'accuracy': f1_data['accuracy'],
                'f1_macro': f1_data['f1_macro'],
                'f1_weighted': f1_data['f1_weighted'],
                'f1_abstention': f1_data['f1_abstention'],
                'abstention_f1_detailed': f1_data['abstention_f1_detailed']['f1_score'],
                'avg_party_cohesion_actual': cohesion_data['actual_cohesion']['average_cohesion'],
                'avg_party_cohesion_predicted': cohesion_data['predicted_cohesion']['average_cohesion'],
                'cohesion_similarity': cohesion_data['cohesion_similarity']['overall_similarity']
            })
        
        summary_df = pd.DataFrame(summary_data)
        csv_path = os.path.join(self.output_dir, 'analysis_summary.csv')
        summary_df.to_csv(csv_path, index=False)
        
        print(f"📊 分析结果已保存:")
        print(f"  - 详细报告: {json_path}")
        print(f"  - 摘要表格: {csv_path}")
        
        return json_path, csv_path

def analyze_results(results_csv_path: str, output_dir: str = None) -> Tuple[str, str]:
    """
    分析结果的主函数
    
    Args:
        results_csv_path: 结果CSV文件路径
        output_dir: 输出目录，如果为None则使用CSV文件所在目录
    
    Returns:
        Tuple[str, str]: (JSON报告路径, CSV摘要路径)
    """
    if output_dir is None:
        output_dir = os.path.dirname(results_csv_path)
    
    print(f"🔍 开始分析结果文件: {results_csv_path}")
    
    # 创建分析器
    analyzer = ResultAnalyzer(results_csv_path, output_dir)
    
    # 生成报告
    report = analyzer.generate_detailed_report()
    
    # 保存结果
    json_path, csv_path = analyzer.save_results(report)
    
    # 打印关键指标摘要
    print(f"\n📈 关键指标摘要:")
    print(f"{'='*60}")
    
    for stage in ['stage_1', 'stage_2', 'stage_3']:
        stage_name = stage.replace('_', ' ').title()
        f1_data = report['f1_scores'][stage]
        cohesion_data = report['party_cohesion'][stage]
        
        print(f"\n{stage_name}:")
        print(f"  准确率: {f1_data['accuracy']:.3f}")
        print(f"  F1分数 (宏平均): {f1_data['f1_macro']:.3f}")
        print(f"  弃权票F1分数: {f1_data['abstention_f1_detailed']['f1_score']:.3f}")
        print(f"  党团凝聚力相似性: {cohesion_data['cohesion_similarity']['overall_similarity']:.3f}")
    
    print(f"\n✅ 分析完成！")
    return json_path, csv_path

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python result_analysis.py <results_csv_path> [output_dir]")
        sys.exit(1)
    
    results_csv_path = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None
    
    analyze_results(results_csv_path, output_dir)
