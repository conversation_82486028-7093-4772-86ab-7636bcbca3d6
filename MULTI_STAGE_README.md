# 多阶段欧洲议会投票模拟系统

这是一个基于大语言模型的多阶段欧洲议会投票模拟系统，模拟真实的政治决策过程。

## 系统特点

### 🎯 三阶段模拟流程
1. **阶段一：初始立场形成** - 议员基于个人背景和提案信息形成初始立场
2. **阶段二：党团会议** - 党团内部交流，议员可能调整立场
3. **阶段三：公开辩论** - 跨党团公开辩论，最终确定投票

### 📊 分层抽样机制
- **党团会议**：按投票倾向（支持/反对/弃权）分层抽样选择发言人，保持原始比例
- **公开辩论**：按党团规模比例选择发言人
- **可配置采样比例**：支持独立设置党团会议和公开辩论的采样比例（默认5%）
- **最小保证**：每个投票类型至少有一名发言人（如果存在）

### 💾 完整数据追踪
- **JSON存储结构**：按提案ID创建文件夹，按党团分组存储
- **完整交互历史**：记录每个阶段的LLM交互和决策过程
- **投票统计**：实时更新各阶段的投票分布
- **三阶段投票记录**：CSV文件包含三个阶段的完整投票轨迹

### 🛡️ 智能错误处理
- **自动重试机制**：LLM调用失败时最多重试3次
- **响应验证**：验证JSON格式和必需字段的完整性
- **默认值后备**：当所有重试失败时使用合理的默认值
- **动态Prompt控制**：自动调整发言人推理长度以控制总Prompt长度

## 文件结构

```
simulation_results/
└── proposal_{vote_id}/
    ├── EPP.json          # 欧洲人民党党团数据
    ├── SD.json           # 社会民主党党团数据
    ├── RENEW.json        # 复兴欧洲党团数据
    ├── GREEN_EFA.json    # 绿党/欧洲自由联盟数据
    ├── ...               # 其他党团
    └── final_results_{vote_id}.csv  # 最终结果CSV
```

### JSON文件结构

每个党团的JSON文件包含：

```json
{
  "group_info": {
    "group_code": "EPP",
    "group_name": "Group of the European People's Party",
    "total_members": 176,
    "vote_id": 164499,
    "vote_title": "Nature Restoration Law"
  },
  "voting_statistics": {
    "stage_1": {"FOR": 45, "AGAINST": 120, "ABSTENTION": 11},
    "stage_2": {"FOR": 50, "AGAINST": 115, "ABSTENTION": 11},
    "stage_3": {"FOR": 48, "AGAINST": 118, "ABSTENTION": 10}
  },
  "agents": [
    {
      "agent_id": "840",
      "vote_id": 164499,
      "static_profile": { ... },      // 议员基本信息
      "dynamic_state": { ... },       // 动态投票状态
      "memory_bank": { ... },         // 记忆库
      "cognitive_outputs": { ... },   // 认知输出
      "interaction_history": { ... }, // 交互历史
      "simulation_metadata": { ... }  // 元数据
    }
  ]
}
```

## 安装和使用

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 确保数据文件存在
# data/personas/members_voting_time.csv
# data/howTheyVoteDataSet/votes.csv
# data/howTheyVoteDataSet/member_votes.csv
# data/debates/speeches.csv
```

### 2. 基本使用

```bash
# 运行单个投票模拟
python src/main_multi_stage.py --single_vote 164499

# 运行指定投票列表
python src/main_multi_stage.py --vote_list 164499 164500 164501

# 运行所有可用投票
python src/main_multi_stage.py

# 自定义参数
python src/main_multi_stage.py \
    --single_vote 164499 \
    --temperature 0.7 \
    --party_sampling_ratio 0.10 \
    --public_sampling_ratio 0.08 \
    --output ./my_results/
```

### 3. 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--output` | `./simulation_results/` | 输出目录 |
| `--temperature` | `0.6` | LLM生成温度 |
| `--party_sampling_ratio` | `0.05` | 党团会议发言人采样比例(5%) |
| `--public_sampling_ratio` | `0.05` | 公开辩论发言人采样比例(5%) |
| `--single_vote` | - | 单个投票ID |
| `--vote_list` | `[]` | 投票ID列表 |

**采样比例说明**：
- 采样比例可以设置为0.01-1.0之间的任意值
- 系统会根据采样比例自动调整Prompt长度和推理截断
- 较高的采样比例会包含更多发言人，但每人的推理会相应缩短

**采样比例测试示例**：
```bash
# 低采样比例测试（更少发言人，更详细推理）
python src/main_multi_stage.py --single_vote 162202 --party_sampling_ratio 0.03 --public_sampling_ratio 0.03

# 中等采样比例测试（平衡发言人数量和推理详细度）
python src/main_multi_stage.py --single_vote 162202 --party_sampling_ratio 0.08 --public_sampling_ratio 0.10

# 高采样比例测试（更多发言人，更简洁推理）
python src/main_multi_stage.py --single_vote 162202 --party_sampling_ratio 0.15 --public_sampling_ratio 0.20

# 不对称采样测试（党团会议和公开辩论使用不同比例）
python src/main_multi_stage.py --single_vote 162202 --party_sampling_ratio 0.05 --public_sampling_ratio 0.12
```

### 4. 测试和调试

```bash
# 运行测试脚本（包含调试信息）
python test_debug_info.py

# 运行基础测试（不调用LLM API）
python test_multi_stage.py
```

**调试功能**：
- 显示第一个议员在每个阶段的完整请求体
- 显示发言人详细信息和分层抽样结果
- 显示三个阶段的投票统计对比

## 系统逻辑详解

### 阶段一：初始立场形成
- **输入**：议员个人信息 + 提案信息 + 辩论发言
- **处理**：每个议员独立分析，形成初始投票意向
- **输出**：投票概率分布 + 推理过程 + 立场强度

### 阶段二：党团会议
- **分层抽样**：按投票倾向选择党团发言人，保持原始比例
  - 计算各投票类型的原始比例
  - 按比例分配发言人数量
  - 确保每个存在的投票类型至少有一名发言人
- **动态Prompt控制**：根据发言人数量动态调整推理长度（最大3000字符）
- **信息交换**：议员了解同党团成员的立场和理由
- **立场调整**：基于党团压力和内部协调调整立场

### 阶段三：公开辩论
- **跨党团抽样**：按党团规模比例选择公开发言人（5%采样）
- **动态Prompt控制**：根据发言人数量动态调整推理长度（最大4000字符）
- **公开辩论**：议员接收来自不同党团的多元化观点
- **最终决策**：综合所有信息做出最终投票决定

### 智能Prompt管理
- **长度控制**：根据采样比例自动调整总Prompt长度限制
- **动态截断**：根据发言人数量和采样比例动态调整每人的推理长度
- **保持完整性**：确保所有发言人都被包含，只调整内容详细程度
- **比例适应**：
  - 5%及以下：推理长度100-250字符，Prompt限制3000-4000字符
  - 5%-10%：推理长度60-200字符，Prompt限制4000-5000字符
  - 10%-20%：推理长度40-150字符，Prompt限制5000-6000字符
  - 20%以上：推理长度30-100字符，Prompt限制6000-8000字符

## 输出结果

### CSV结果文件
与原系统兼容的CSV格式，但包含三阶段完整数据：
- `member_id`: 议员ID
- `vote_id`: 投票ID
- `fullName`: 议员姓名
- `reasonings`: **三个阶段的推理过程列表** `[阶段一推理, 阶段二推理, 阶段三推理]`
- `llm_votes`: **三个阶段的投票结果列表** `[阶段一投票, 阶段二投票, 阶段三投票]`
- `actual_vote`: 实际投票
- `group`: 党团代码
- `country`: 国家代码

**重要**：`reasonings` 和 `llm_votes` 字段现在是包含三个元素的列表，分别对应三个阶段的结果。

### JSON详细数据
完整的模拟过程数据，包括：
- 每个阶段的交互记录
- 立场变化轨迹
- 党团内部和跨党团影响
- 发言人选择和影响分析

## 扩展功能

### 自定义采样策略
可以修改 `MultiStageSimulation` 类中的采样方法：
- `_select_party_speakers()`: 党团发言人选择
- `_select_public_speakers()`: 公开辩论发言人选择

### 添加新的交互阶段
系统设计支持扩展，可以添加更多交互回合或阶段。

### 分析工具
JSON数据支持深度分析：
- 立场变化轨迹分析
- 影响力网络分析  
- 党团内部一致性分析
- 跨党团说服效果分析

## 注意事项

1. **API调用量**：三阶段模拟会显著增加LLM API调用次数（约为原系统的3倍）
2. **数据完整性**：确保所有必需的数据文件都存在
3. **内存使用**：大规模模拟可能需要较多内存
4. **错误处理**：系统包含完善的错误处理和重试机制
5. **Prompt长度**：系统自动控制Prompt长度，但大量发言人可能影响性能
6. **CSV格式变化**：注意新的CSV格式包含三阶段数据列表

## 故障排除

### 常见问题
1. **找不到投票数据**：检查 `data/` 目录下的CSV文件
2. **LLM API错误**：检查API密钥和网络连接
3. **JSON解析错误**：LLM输出格式问题，系统会自动重试

### 调试模式
使用测试脚本进行调试：
```bash
# 完整调试信息（包含LLM交互）
python test_debug_info.py

# 基础测试（不调用LLM）
python test_multi_stage.py
```

### 性能优化建议
1. **小规模测试**：先用少量议员测试（如单个党团）
2. **监控API使用**：注意API调用频率限制
3. **分批处理**：大规模模拟可以分多次运行
4. **结果备份**：定期备份中间结果JSON文件
