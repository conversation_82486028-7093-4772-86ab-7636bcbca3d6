#!/usr/bin/env python3
"""
演示结果分析功能
"""

import sys
import os
import pandas as pd
import json

# 添加src目录到路径
sys.path.append('src')

def demo_analysis_results():
    """演示分析结果"""
    print("结果分析功能演示")
    print("=" * 60)
    
    # 检查现有的分析结果
    analysis_dir = "simulation_results/proposal_162202"
    summary_file = os.path.join(analysis_dir, "analysis_summary.csv")
    json_file = os.path.join(analysis_dir, "analysis_report.json")
    
    if os.path.exists(summary_file):
        print(f"\n分析摘要 (来自 {summary_file}):")
        print("-" * 60)
        
        df = pd.read_csv(summary_file)
        
        for _, row in df.iterrows():
            stage = row['stage'].replace('_', ' ').title()
            print(f"\n{stage}:")
            print(f"  📈 F1分数 (宏平均): {row['f1_macro']:.3f}")
            print(f"  📈 F1分数 (加权): {row['f1_weighted']:.3f}")
            print(f"  🎯 弃权票F1分数: {row['f1_abstention']:.3f}")
            print(f"  🤝 党团凝聚力相似性: {row['cohesion_similarity']:.3f}")
            print(f"  📊 实际党团凝聚力: {row['avg_party_cohesion_actual']:.3f}")
            print(f"  📊 预测党团凝聚力: {row['avg_party_cohesion_predicted']:.3f}")
    
    if os.path.exists(json_file):
        print(f"\n🔍 详细分析 (来自 {json_file}):")
        print("-" * 60)
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        metadata = data['metadata']
        print(f"\n📋 基本信息:")
        print(f"  总议员数: {metadata['total_members']}")
        print(f"  党团数量: {metadata['total_groups']}")
        print(f"  分析时间: {metadata['analysis_timestamp']}")
        
        # 显示弃权票详细分析
        print(f"\n🎯 弃权票预测详细分析:")
        for stage in ['stage_1', 'stage_2', 'stage_3']:
            stage_name = stage.replace('_', ' ').title()
            abstention_data = data['f1_scores'][stage]['abstention_f1_detailed']
            
            print(f"\n  {stage_name}:")
            print(f"    精确率: {abstention_data['precision']:.3f}")
            print(f"    召回率: {abstention_data['recall']:.3f}")
            print(f"    F1分数: {abstention_data['f1_score']:.3f}")
            print(f"    实际弃权票数: {abstention_data['total_actual_abstentions']}")
            print(f"    预测弃权票数: {abstention_data['total_predicted_abstentions']}")
        
        # 显示党团凝聚力分析
        print(f"\n🤝 党团凝聚力分析 (Stage 3):")
        cohesion_data = data['party_cohesion']['stage_3']
        actual_cohesion = cohesion_data['actual_cohesion']['by_group']
        predicted_cohesion = cohesion_data['predicted_cohesion']['by_group']
        similarity = cohesion_data['cohesion_similarity']['by_group']
        
        for group in sorted(actual_cohesion.keys()):
            if group in predicted_cohesion and group in similarity:
                print(f"\n  {group}:")
                print(f"    实际凝聚力: {actual_cohesion[group]['cohesion_score']:.3f}")
                print(f"    预测凝聚力: {predicted_cohesion[group]['cohesion_score']:.3f}")
                print(f"    相似性: {similarity[group]:.3f}")
                print(f"    成员数: {actual_cohesion[group]['total_members']}")
    
    print(f"\n" + "=" * 60)
    print("✅ 演示完成！")
    print(f"\n📝 功能说明:")
    print("1. F1分数：整体预测准确性，与原文保持一致")
    print("2. 弃权票F1分数：专门评估对'弃权'这一难点的预测能力")
    print("3. 党团凝聚力得分：评估模型是否能真实模拟党团内部聚合效应")
    print(f"\n🚀 使用方法:")
    print("- 运行模拟后会自动生成分析结果")
    print("- 也可以手动运行: python src/result_analysis.py <csv_file>")

if __name__ == "__main__":
    demo_analysis_results()
