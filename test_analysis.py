#!/usr/bin/env python3
"""
测试结果分析功能
"""

import sys
import os

# 添加src目录到路径
sys.path.append('src')

from result_analysis import analyze_results

def test_analysis():
    """测试分析功能"""
    print("🧪 测试结果分析功能")
    print("=" * 50)
    
    # 使用现有的结果文件进行测试
    csv_path = "simulation_results/proposal_162202/final_results_162202.csv"
    
    if not os.path.exists(csv_path):
        print(f"❌ 测试文件不存在: {csv_path}")
        return False
    
    try:
        print(f"📁 测试文件: {csv_path}")
        
        # 运行分析
        json_path, summary_path = analyze_results(csv_path)
        
        print(f"\n✅ 分析成功完成!")
        print(f"📊 JSON报告: {json_path}")
        print(f"📋 CSV摘要: {summary_path}")
        
        # 验证文件是否生成
        if os.path.exists(json_path) and os.path.exists(summary_path):
            print(f"\n🎉 所有文件都已成功生成!")
            return True
        else:
            print(f"\n❌ 某些文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_analysis()
    if success:
        print(f"\n✅ 测试通过!")
    else:
        print(f"\n❌ 测试失败!")
