#!/usr/bin/env python3
"""
测试完整的结果分析系统
"""

import os
import sys
import pandas as pd
import json

# 添加src目录到路径
sys.path.append('src')

def test_analysis_files():
    """测试分析文件是否存在且格式正确"""
    print("测试结果分析系统")
    print("=" * 50)
    
    # 检查分析结果文件
    analysis_dir = "simulation_results/proposal_162202"
    summary_file = os.path.join(analysis_dir, "analysis_summary.csv")
    json_file = os.path.join(analysis_dir, "analysis_report.json")
    
    success = True
    
    # 测试CSV摘要文件
    if os.path.exists(summary_file):
        print(f"✓ CSV摘要文件存在: {summary_file}")
        try:
            df = pd.read_csv(summary_file)
            expected_columns = ['stage', 'f1_macro', 'f1_weighted', 'f1_abstention', 
                              'abstention_f1_detailed', 'avg_party_cohesion_actual', 
                              'avg_party_cohesion_predicted', 'cohesion_similarity']
            
            if all(col in df.columns for col in expected_columns):
                print("✓ CSV文件格式正确")
                print(f"  - 包含 {len(df)} 个阶段的数据")
                
                # 显示关键指标
                for _, row in df.iterrows():
                    stage = row['stage']
                    print(f"  - {stage}: F1={row['f1_macro']:.3f}, 弃权F1={row['f1_abstention']:.3f}, 凝聚力相似性={row['cohesion_similarity']:.3f}")
            else:
                print("✗ CSV文件格式不正确")
                success = False
        except Exception as e:
            print(f"✗ 读取CSV文件失败: {e}")
            success = False
    else:
        print(f"✗ CSV摘要文件不存在: {summary_file}")
        success = False
    
    # 测试JSON详细报告
    if os.path.exists(json_file):
        print(f"\n✓ JSON报告文件存在: {json_file}")
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查必要的键
            required_keys = ['metadata', 'f1_scores', 'party_cohesion', 'classification_reports']
            if all(key in data for key in required_keys):
                print("✓ JSON文件结构正确")
                
                # 显示元数据
                metadata = data['metadata']
                print(f"  - 总议员数: {metadata['total_members']}")
                print(f"  - 党团数量: {metadata['total_groups']}")
                
                # 检查三个阶段的数据
                stages = ['stage_1', 'stage_2', 'stage_3']
                for stage in stages:
                    if stage in data['f1_scores'] and stage in data['party_cohesion']:
                        print(f"  - {stage} 数据完整")
                    else:
                        print(f"  ✗ {stage} 数据缺失")
                        success = False
                
                # 显示弃权票分析示例
                stage3_abstention = data['f1_scores']['stage_3']['abstention_f1_detailed']
                print(f"\n弃权票分析示例 (Stage 3):")
                print(f"  - 实际弃权票: {stage3_abstention['total_actual_abstentions']}")
                print(f"  - 预测弃权票: {stage3_abstention['total_predicted_abstentions']}")
                print(f"  - F1分数: {stage3_abstention['f1_score']:.3f}")
                
                # 显示党团凝聚力示例
                cohesion_data = data['party_cohesion']['stage_3']
                actual_avg = cohesion_data['actual_cohesion']['average_cohesion']
                predicted_avg = cohesion_data['predicted_cohesion']['average_cohesion']
                similarity = cohesion_data['cohesion_similarity']['overall_similarity']
                
                print(f"\n党团凝聚力分析示例 (Stage 3):")
                print(f"  - 实际平均凝聚力: {actual_avg:.3f}")
                print(f"  - 预测平均凝聚力: {predicted_avg:.3f}")
                print(f"  - 整体相似性: {similarity:.3f}")
                
            else:
                print("✗ JSON文件结构不正确")
                success = False
        except Exception as e:
            print(f"✗ 读取JSON文件失败: {e}")
            success = False
    else:
        print(f"✗ JSON报告文件不存在: {json_file}")
        success = False
    
    return success

def test_analysis_script():
    """测试分析脚本是否可以正常导入和运行"""
    print(f"\n测试分析脚本导入")
    print("-" * 30)
    
    try:
        from result_analysis import ResultAnalyzer, analyze_results
        print("✓ 成功导入分析模块")
        
        # 测试分析器类
        csv_path = "simulation_results/proposal_162202/final_results_162202.csv"
        if os.path.exists(csv_path):
            analyzer = ResultAnalyzer(csv_path, "simulation_results/proposal_162202")
            print("✓ 成功创建分析器实例")
            
            # 测试数据解析
            if hasattr(analyzer, 'df') and len(analyzer.df) > 0:
                print(f"✓ 成功解析数据，共 {len(analyzer.df)} 条记录")
                return True
            else:
                print("✗ 数据解析失败")
                return False
        else:
            print(f"✗ 测试数据文件不存在: {csv_path}")
            return False
            
    except Exception as e:
        print(f"✗ 导入分析模块失败: {e}")
        return False

def main():
    """主测试函数"""
    print("完整系统测试")
    print("=" * 60)
    
    # 测试分析文件
    files_ok = test_analysis_files()
    
    # 测试分析脚本
    script_ok = test_analysis_script()
    
    # 总结
    print(f"\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if files_ok and script_ok:
        print("✓ 所有测试通过！")
        print("\n系统功能:")
        print("1. ✓ F1分数计算 - 整体预测准确性评估")
        print("2. ✓ 弃权票F1分数 - 专门评估弃权预测能力")
        print("3. ✓ 党团凝聚力得分 - 评估党团内部聚合效应模拟")
        print("4. ✓ 自动分析集成 - 模拟完成后自动运行分析")
        
        print("\n使用方法:")
        print("- 运行模拟: python src/main_multi_stage.py --single_vote 162202")
        print("- 手动分析: python src/result_analysis.py <csv_file>")
        
        return True
    else:
        print("✗ 部分测试失败")
        if not files_ok:
            print("  - 分析文件测试失败")
        if not script_ok:
            print("  - 分析脚本测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
