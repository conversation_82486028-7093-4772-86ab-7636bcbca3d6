{
  // ========== 根级别字段 ==========

  // 智能体唯一标识符
  // 来源: members_voting_time.csv的member_id字段
  // 用途: 标识特定的议员智能体，用于文件命名和引用
  // 修改时机: 初始化时设置，之后不变
  "agent_id": "840",

  // 投票提案ID
  // 来源: votes.csv的id字段
  // 用途: 标识当前模拟的具体投票提案
  // 修改时机: 每次新投票模拟时设置，单次模拟中不变
  "vote_id": 164499,

  // ========== 身份画像模块 ==========
  // 定义智能体的基本身份和初始政治倾向，作为决策基础
  "static_profile": {

    // 基础个人信息
    // 来源: members_voting_time.csv
    // 用途: 提供议员的基本身份信息，用于生成persona描述和LLM交互
    // 修改时机: 初始化时设置，之后不变
    "basic_info": {
      // 议员ID，与agent_id相同
      // 来源: members_voting_time.csv的member_id字段
      "member_id": 840,

      // 议员全名
      // 来源: members_voting_time.csv的fullname字段
      // 用途: 用于生成persona描述，最终CSV输出
      "fullname": "Charles Goerens",

      // 性别 ("male"/"female")
      // 来源: members_voting_time.csv的gender字段
      // 用途: 用于生成persona描述中的代词选择
      "gender": "male",

      // 出生日期 (YYYY-MM-DD格式)
      // 来源: members_voting_time.csv的date_of_birth字段
      // 用途: 计算年龄，用于persona描述
      "date_of_birth": "1952-02-06",

      // 年龄
      // 来源: 程序计算 - 根据date_of_birth和投票时间通过calculate_age函数计算
      // 用途: 用于persona描述
      // 修改时机: 每次新投票模拟时重新计算
      "age": 71,

      // 出生地
      // 来源: members_voting_time.csv的birthplace字段
      // 用途: 用于persona描述
      "birthplace": "Ettelbruck",

      // 代表国家
      // 来源: members_voting_time.csv的country字段
      // 用途: 用于persona描述和分析
      "country": "Luxembourg",

      // 国家代码
      // 来源: member_votes.csv的country_code字段
      // 用途: 最终CSV输出
      "country_code": "LUX"
    },

    // 政治身份信息
    // 来源: members_voting_time.csv, groups.csv
    // 用途: 定义议员的政治归属，影响党团内交互和立场形成
    // 修改时机: 初始化时设置，之后不变
    "political_identity": {
      // 欧洲议会党团代码
      // 来源: members_voting_time.csv的group_code字段
      // 用途: 党团内交互分组，最终CSV输出
      "group_code": "RENEW",

      // 欧洲议会党团全名
      // 来源: members_voting_time.csv的group_name字段
      // 用途: 用于persona描述
      "group_name": "Renew Europe Group",

      // 国家政党名称
      // 来源: members_voting_time.csv的national_party字段
      // 用途: 用于persona描述
      "national_party": "Parti démocratique",

      // 议会届期
      // 来源: members_voting_time.csv的term字段
      // 用途: 确定议员任期有效性
      "term": 9,

      // 任期开始日期
      // 来源: members_voting_time.csv的start_date字段
      // 用途: 验证议员在投票时是否在任
      "start_date": "2019-07-02",

      // 任期结束日期
      // 来源: members_voting_time.csv的end_date字段
      // 用途: 验证议员在投票时是否在任
      "end_date": "2024-07-15"
    },

    // 人格描述文本
    // 来源: 程序生成 - 由_create_persona_description方法基于basic_info和political_identity生成
    // 用途: 作为LLM prompt的一部分，定义智能体的身份角色
    // 修改时机: 初始化时生成，之后不变
    "persona_description": "Charles Goerens, a male politician, 71 years old, born in Ettelbruck. He is representing the country Luxembourg. He is not part of a national party. He is part of the Renew Europe Group."
  },

  // ========== 动态状态模块 ==========
  // 维护智能体在模拟过程中的动态状态
  "dynamic_state": {

    // 当前投票意向的概率分布
    // 来源: 大模型返回数据 - LLM交互结果
    // 用途: 表示智能体对三个投票选项的倾向程度
    // 修改时机: 每次LLM交互后更新
    // 注意: 三个概率之和应为1.0
    "current_voting_intention": {
      "FOR": 0.33,        // 支持票的概率
      "AGAINST": 0.33,    // 反对票的概率
      "ABSTENTION": 0.34  // 弃权票的概率
    },

    // 立场强度/置信度 (0-1)
    // 来源: 大模型返回数据 - 要求LLM在每次交互时返回对自己立场的置信度评分
    // 用途: 表示智能体对自己当前立场的确信程度
    // 修改时机: 每次LLM交互后更新
    "stance_confidence": 0.5,

    // 最后更新时间戳
    // 来源: 程序生成 - 每次状态更新时的时间戳
    // 用途: 追踪状态更新时间
    // 修改时机: 每次状态更新时设置
    "last_updated": "2024-01-01T00:00:00Z",

    // 状态更新历史记录
    // 来源: 大模型返回数据 + 程序控制信息
    // 用途: 追踪智能体立场变化过程
    // 修改时机: 每次状态更新时添加新记录
    "update_history": [
      {
        // 阶段名称 - 程序控制信息
        "stage": "initial",
        // 回合数 - 程序控制信息
        "round": 0,
        // 更新时间戳 - 程序生成
        "timestamp": "2024-01-01T00:00:00Z",
        // 当时的投票意向 - 大模型返回数据
        "voting_intention": {
          "FOR": 0.33,
          "AGAINST": 0.33,
          "ABSTENTION": 0.34
        },
        // 当时的立场强度 - 大模型返回数据
        "stance_confidence": 0.5,
        // 触发更新的原因 - 程序控制信息
        "trigger": "initial_stance_formation"
      }
    ]
  },

  // ========== 记忆库 ==========
  // 存储智能体接收到的关键信息和论点
  "memory_bank": {

    // 接收到的论点分类存储
    // 来源: 原始数据集(speeches.csv) + 大模型返回数据(其他议员的输出)
    // 用途: 存储智能体在交互过程中接收到的各种论点
    // 修改时机: 每次接收到新论点时更新
    "received_arguments": {
      // 支持提案的论点
      // 结构: [{content: string, speaker_id: string}, ...]
      "pro_arguments": [],
      // 反对提案的论点
      "contra_arguments": [],
      // 中性/弃权相关的论点
      "neutral_arguments": []
    },

    // 党团内部沟通记录
    // 来源: 大模型返回数据 - 其他党团成员的投票意向和发言
    // 用途: 存储党团会议中的信息交换
    // 修改时机: 党团会议阶段更新
    // 结构: [{member_id: string, voting_intention: object, message: string}, ...]
    "party_group_communications": [],

    // 公开辩论中接收的信息
    // 来源: 原始数据集(speeches.csv) + 大模型返回数据
    // 用途: 存储公开辩论阶段的输入信息
    // 修改时机: 公开辩论阶段更新
    // 结构: [{speaker_id: string, speaker_group: string, argument_type: string, content: string}, ...]
    "public_debate_inputs": [],

    // 有影响力的发言人列表
    // 来源: 大模型返回数据 - 要求LLM识别对其有影响的发言人
    // 用途: 记录对该智能体有特殊影响的发言人
    // 修改时机: 根据LLM交互结果更新
    "influential_speakers": []
  },

  // ========== 认知模块输出 ==========
  // 记录智能体的思考和决策过程
  "cognitive_outputs": {

    // 初始立场分析结果
    // 来源: 大模型返回数据 - 阶段一LLM交互输出
    // 用途: 记录智能体的初始思考过程
    // 修改时机: 阶段一完成时设置
    "initial_stance_analysis": {
      // 推理过程文本 - 来源: LLM输出的reasoning字段，用途: 最终CSV输出的reasonings字段
      "reasoning": "",
      // 关键考虑因素 - 来源: 大模型返回数据，要求LLM列出决策时的关键考虑因素
      "key_considerations": [],
      // 生成时间戳 - 程序生成
      "generated_at": null
    },

    // 智能体生成的论点
    // 来源: 大模型返回数据
    // 用途: 存储智能体在辩论中生成的论点
    // 修改时机: 辩论阶段生成论点时更新
    "generated_arguments": {
      // 自己生成的支持论点 - 来源: 大模型返回数据，要求LLM生成支持提案的论点
      "own_pro_arguments": [],
      // 自己生成的反对论点 - 来源: 大模型返回数据，要求LLM生成反对提案的论点
      "own_contra_arguments": [],
      // 生成时间戳 - 程序生成
      "generated_at": null
    },

    // 立场更新记录
    // 来源: 大模型返回数据 - 要求LLM解释立场变化的原因
    // 用途: 详细记录每次立场变化的原因和过程
    // 修改时机: 每次立场更新时添加记录
    // 结构: [{reasoning: string, before_intention: object, after_intention: object}, ...]
    "stance_updates": []
  },
  
  // ========== 交互历史 ==========
  // 追踪各阶段的交互过程和结果
  "interaction_history": {

    // 阶段一：初始立场形成
    // 读取: static_profile, simulation_metadata.proposal_info, simulation_metadata.debate_speeches
    // 修改: dynamic_state, cognitive_outputs.initial_stance_analysis, interaction_history.stage_1_initial
    "stage_1_initial": {
      // 阶段是否完成 - 程序控制信息
      "completed": false,
      // LLM交互记录 - 大模型返回数据
      // 结构: [{response: string, parsed_output: object}, ...]
      "llm_interactions": [],
      // 该阶段结束时的最终投票意向 - 大模型返回数据
      "final_voting_intention": null,
      // 该阶段结束时的最终立场强度 - 大模型返回数据
      "final_stance_confidence": null
    },

    // 阶段二：党团核心会议
    // 读取: static_profile.political_identity.group_code, dynamic_state.current_voting_intention, 同党团其他议员数据
    // 修改: dynamic_state, memory_bank.party_group_communications, interaction_history.stage_2_party_meeting
    "stage_2_party_meeting": {
      "completed": false,
      // 接收到的党团成员立场 - 大模型返回数据(其他党团成员的LLM输出)
      // 结构: [{member_id: string, voting_intention: object, confidence: float}, ...]
      "received_party_positions": [],
      // 党团领袖发言记录 - 大模型返回数据(党团领袖的LLM生成发言)
      // 结构: [{leader_id: string, statement: string}, ...]
      "party_leader_statements": [],
      "llm_interactions": [],
      "final_voting_intention": null,
      "final_stance_confidence": null
    },

    // 阶段二：公开辩论
    // 读取: dynamic_state, memory_bank.party_group_communications, 发言人的generated_arguments
    // 修改: dynamic_state, memory_bank.received_arguments, memory_bank.public_debate_inputs, interaction_history.stage_2_public_debate
    "stage_2_public_debate": {
      "completed": false,
      // 接收到的公开论点 - 原始数据集(speeches.csv) + 大模型返回数据
      // 结构: [{speaker_id: string, speaker_group: string, argument: string, argument_type: string}, ...]
      "received_public_arguments": [],
      // 发言人影响记录 - 大模型返回数据(要求LLM评估各发言人的影响程度)
      // 结构: [{speaker_id: string, influence_score: float, influence_reason: string}, ...]
      "speaker_influences": [],
      "llm_interactions": [],
      "final_voting_intention": null,
      "final_stance_confidence": null
    },

    // 阶段三：最终投票
    // 读取: dynamic_state, memory_bank(完整)
    // 修改: interaction_history.stage_3_final
    "stage_3_final": {
      "completed": false,
      // 最终投票结果 ("FOR", "AGAINST", "ABSTENTION") - 大模型返回数据，用途: 最终CSV输出的llm_votes字段
      "final_vote": null,
      // 最终投票的推理说明 - 大模型返回数据，要求LLM提供最终决策的完整推理
      "final_reasoning": null
    }
  },
  
  // ========== 模拟元数据 ==========
  "simulation_metadata": {

    // 提案信息
    "proposal_info": {
      // 投票标题 - 来源: votes.csv的display_title字段
      "vote_title": "Nature Restoration Law",
      // 提案摘要 - 如果原始数据集中没有则删除此字段
      "proposal_summary": "",
      // 辩论发言内容 - 来源: speeches.csv或counterfactual_speeches.csv的相关字段，用途: 提供给智能体的辩论背景信息
      "debate_speeches": []
    },

    // 该议员的实际投票结果 - 来源: member_votes.csv的position字段，用途: 最终CSV输出的actual_vote字段，用于对比分析
    "actual_vote": null,

    // JSON文件创建时间戳 - 程序生成
    "created_at": "2024-01-01T00:00:00Z",

    // 最后修改时间戳 - 程序生成，修改时机: 每次文件更新时设置
    "last_modified": "2024-01-01T00:00:00Z",

    // 模拟版本号 - 程序控制，用途: 追踪不同版本的模拟实验
    "simulation_version": "1.0"
  }

  // ========== 数据来源总结 ==========
  // 1. 原始数据集来源:
  //    - members_voting_time.csv: basic_info, political_identity
  //    - member_votes.csv: country_code, actual_vote
  //    - votes.csv: vote_id, vote_title
  //    - speeches.csv: debate_speeches, 部分received_arguments
  //
  // 2. 大模型返回数据:
  //    - current_voting_intention, stance_confidence
  //    - cognitive_outputs的所有内容
  //    - memory_bank的大部分内容
  //    - interaction_history中的LLM相关字段
  //
  // 3. 程序计算/生成:
  //    - age (基于date_of_birth计算)
  //    - persona_description (基于basic_info和political_identity生成)
  //    - 各种时间戳和控制字段
  //
  // 4. 需要删除的字段(无明确来源):
  //    - initial_political_stance (需要预定义映射表)
}
